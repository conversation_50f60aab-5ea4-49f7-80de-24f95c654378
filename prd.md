# 台球计分系统产品需求文档（PRD）

## 目录

1. [项目概述](#1-项目概述)
2. [需求分析](#2-需求分析)
   - 2.1 [核心维度](#21-核心维度)
   - 2.2 [扩展性需求](#22-扩展性需求)
3. [用户使用流程](#3-用户使用流程)
   - 3.1 [用户核心页面流程](#31-用户核心页面流程)
   - 3.2 [最小化用户操作流程](#32-最小化用户操作流程)
   - 3.3 [数据记录结构细化](#33-数据记录结构细化)
4. [核心数据结构](#4-核心数据结构)
   - 4.1 [比赛配置](#41-比赛配置matchconfiguration)
   - 4.2 [比赛记录](#42-比赛记录matchrecord)
   - 4.3 [球设置](#43-球设置ballconfiguration)
   - 4.4 [游戏配置](#44-游戏配置gameconfiguration)
   - 4.5 [球属性变化规则](#45-球属性变化规则ballattributechangerule)
   - 4.6 [桌面状态](#46-桌面状态tablestate)
   - 4.7 [选手记录](#47-选手记录playerrecord)
   - 4.8 [游戏状态](#48-游戏状态gamestate)
   - 4.9 [规则引擎](#49-规则引擎ruleengine)
   - 4.10 [局记录](#410-局记录framerecord)
   - 4.11 [轮记录](#411-轮记录turnrecord)
5. [界面设计](#5-界面设计)
   - 5.1 [比赛配置界面](#51-比赛配置界面)
   - 5.2 [游戏设置界面](#52-游戏设置界面)
   - 5.3 [动态布局系统](#53-动态布局系统)
   - 5.4 [游戏主界面](#54-游戏主界面)
   - 5.5 [高级统计面板](#55-高级统计面板)
   - 5.6 [规则帮助系统](#56-规则帮助系统)
   - 5.7 [界面设计优化](#57-界面设计优化)
   - 5.8 [比赛计分界面优化](#58-比赛计分界面优化)
   - 5.9 [统计界面优化](#59-统计界面优化)
6. [系统架构](#6-系统架构)
   - 6.1 [模块化设计](#61-模块化设计)
   - 6.2 [事件驱动机制](#62-事件驱动机制)
   - 6.3 [配置持久化](#63-配置持久化)
   - 6.4 [插件系统](#64-插件系统)
7. [交互流程设计](#7-交互流程设计)
   - 7.1 [错误防护机制](#71-错误防护机制)
   - 7.2 [反馈机制设计](#72-反馈机制设计)
8. [未来扩展计划](#8-未来扩展计划)
9. [实现路线图](#9-实现路线图)
   - 9.1 [第一阶段：核心功能实现](#91-第一阶段核心功能实现)
   - 9.2 [第二阶段：扩展性增强](#92-第二阶段扩展性增强)
   - 9.3 [第三阶段：用户体验优化](#93-第三阶段用户体验优化)
   - 9.4 [第四阶段：插件系统与扩展功能](#94-第四阶段插件系统与扩展功能)

## 1. 项目概述

台球计分系统是一个基于Vue 3和TypeScript开发的Web应用，用于记录和管理台球比赛的计分和规则执行。
该系统支持灵活的配置，包括可变的选手数量、球数设置、得分规则等，为玩家提供了一个直观、易用的界面来跟踪比赛进程和得分情况。

## 2. 需求分析

### 2.1 核心维度

#### 2.1.1 时间进程维度

- **比赛(Match)**: 整个比赛活动的全过程
  - 比赛的组织方式：
    - 固定局数：预先设定打满多少局结束比赛
    - 固定时间：可以是设定截止时间点(如下午5点)，也可以是持续时长(如3小时)
    - 固定分数：当某位选手达到预设的目标分数时结束比赛
    - 手动结束：不设限制，由用户手动决定结束时机
  - 比赛需记录：所有局的详细信息、参与选手的最终得分和统计数据、比赛的开始和结束时间等
- **局(Frame)**: 一组球被打完的过程
  - 局的结束条件：桌面的球被打完
  - 每局需记录：参与的选手、每个选手的得分、特殊事件、胜者信息等
- **轮(Turn)**: 一个选手连续击球的过程
  - 轮的结束条件：①未进球换人 ②犯规 ③本局结束
  - 每轮需记录：谁在击球、进了哪些球、有没有犯规、得失分情况等

#### 2.1.2 参赛选手维度

- 选手之间轮流击球的转换规则

#### 2.1.3 选手转换与得分规则

##### 选手顺序与转换

1. **基本顺序**：
   - 参与比赛的选手按固定顺序排列，各有明确角色（当前选手、后序选手、前序选手）。
   - 首局顺序随机确定或按约定规则设置。
   - 之后每局由上一局胜者开球（作为当前选手），上局败者排第二击球（作为后序选手），剩余选手按原顺序排列。
   - 例如：上一局B为胜者，A为败者，则本局顺序为：B(当前)→A(后序)→C(前序)。

2. **击球规则**：
   - 当前选手必须按从小到大的顺序击球，首先击中台面上号码最小的球。
   - 击球结果决定下一步行动：

     a. **开球轮**：
     - 进球：继续击球
     - 不进球：轮换到下一个选手
     - 犯规：按犯规规则处理，轮换到下一个选手

     b. **非开球轮**：
     - 进球：
       - 如果击中最小号球后，任何球进袋都是有效进球（不限于最小号球）
       - 有效进球后可以继续击球
     - 不进球：轮换到下一个选手
     - 犯规：按犯规规则处理，轮换到下一个选手
     - 让球：主动放弃击球权，轮换到下一个选手

3. **轮换规则**：
   - 每次换人击打称为一轮新的开始。
   - 每轮开始时，上一轮的后序选手成为当前轮的当前选手，上一轮的当前选手成为当前轮的前序选手，上一轮的前序选手成为当前轮的后序选手。

##### 得分计算规则

1. **基本结算原则**：
   - 当前选手的得分或失分与前序选手结算。
   - 让球后，前序选手变为原后序选手，结算关系对应变化。
   - 得分计算按轮结算，本轮有清台得分（如大金、小金、黑大金、黑小金）时，本轮内之前的普通进袋得分不重复计算，仅计算清台得分。

2. **开球特殊计分（3人及以上）**：
   - **黄金开球 (Golden Break)**：开球轮，当前选手合法打进得分球（非9号球），获得 `进球总分值 * 2`，其余每位选手扣除 `进球总分值`。
   - **黑金开球 (Black Break)**：开球轮，当前选手犯规并打进得分球（非9号球），扣除 `进球总分值 * 2`，其余每位选手获得 `进球总分值`。
   - *此规则优先于普通进球得分和下方犯规进球规则，且需在游戏配置中启用 `breakShotPoints: 'double'`。*

3. **普通情况进袋得分**：
   - **常规进球**：当前选手击球未犯规，每进一个得分球n得该球分值，前序选手扣该球分值。
   - **黑n**：当前选手进球并犯规，每进一个得分球n扣球分值，前序选手得该球分值。
   - **犯规未进球**：当前选手未进球并犯规，当前选手扣除 `游戏配置` 中设定的 `foulPenaltyPoints` 分数。罚分的去向根据 `foulPointsHandler` 配置决定（给前序选手或累计到犯规池）。

4. **清台得分**：
   - **大金**：开球轮内连续打完全部目标球且未犯规，得[大金分数]×2，另外两个选手各扣大金分数。（需启用 `slamPoints`）
   - **黑大金**：开球轮内连续进球打完全部目标球，但打最后一个球犯规，扣大金分数×2，另外两个选手各得大金分数。
   - **小金**：非开球轮，一轮内连续进球打完全部[小金球组]且未犯规，得[小金分数]，前序选手扣[小金分数]。
   - **黑小金**：非开球轮，一轮内连续进球打完全部[小金球组]且打最后一个球犯规，扣[小金分数]，前序选手得[小金分数]。

5. **让球后的得分结算**：
   - 让球前的进球结算关系不变，仍与原前序选手结算。
   - 让球后的进球与新前序选手（原后序选手）结算。
   - 让球后发生清台（小金/大金等），仍按照让球后的结算关系计算。

6. **示例**（三人情况）：
   - 初始顺序：A（当前）→ B（后序）→ C（前序）
   - A击球进了3号球，与C结算得分
   - A继续击球触发让球条件，B选择让球，顺序变为：A（当前）→ C（后序）→ B（前序）
   - A继续击球进了5号球，这个得分与B结算
   - 若A继续清台（小金），清台的分数与B结算，且之前的3号球得分仍与C结算，5号球得分不单独计算（因为已包含在小金中）
   - 若A未能继续进球或犯规，换人，下一轮顺序恢复为：B（当前）→ C（后序）→ A（前序）

##### 胜负判定规则

1. **开球时胜负**：
   - **开出黄金9**：当前选手开球将9号球打进且未犯规，则直接获胜，本局结束，其后序选手为败者。
   - **开出黄金黑9**：当前选手开球将9号球打进但同时犯规，则后序选手获胜，当前选手为败者。
   - 此规则适用于开球轮的情况，不论是否有其他球进袋。

2. **非开球时胜负**：
   - **正常获胜**：当前选手在合法击球中将9号球打进袋并未犯规，直接获胜。其前序选手为败者。
   - **犯规失败**：当前选手在将9号球打进袋并犯规，其前序选手获胜，当前选手为败者。

3. **胜负与得分关系**：
   - 胜负判定优先于得分计算，一旦触发胜负条件，本局立即结束。
   - 对于计分统计，仍然按照得分规则记录相关得分，但不影响胜负结果。

### 2.2 扩展性需求

- 桌面总目标球数可配置，但是全部球组的最大号码固定是9，比如9球追分，球组是{123456789}，7球追分球组是{1234569}，依次类推。
- 选手数量可灵活设置（2人、3人或更多）
- 每个球对不同选手可有不同属性（得分球、结束球）
- 球的属性可在比赛中根据规则动态变化
- 支持多种不同的比赛规则和得分计算方式
- 支持不同的比赛组织方式（固定局数、固定时间、固定分数、手动结束）

## 3. 用户使用流程

### 3.1 用户核心页面流程

#### 3.1.1 比赛配置界面

- **基本配置区**
  - 选手数量设置（2-N人）
  - 选手姓名/昵称输入
  - 比赛模式选择：计时赛/固定比分/固定局数/手动结束
  - 计时赛设置：时间长度或截止时间
  - 固定比分设置：目标得分
  - 固定局数设置：局数上限

- **球组配置区**
  - 总球数设置（支持3-9球）
  - 每个选手的球组设置面板
    - 需打的球组（例如选手A需打1234567）
    - 每个球的分值设置（例如5号1分，7号2分）
    - 结束球设置（例如打进7号球本局结束）
    - 清台认定球组设置（例如清台球组为12345678）

- **规则配置区**
 1、遮挡球后是否允许让球。
 2、得分球传进是否延（三选：延1颗、延两颗、一直延）  
 3、清台模式选择（全球金/1-9金）3、犯规几分，犯规罚分处理：上家/累计到犯规池。
 4、如果是两人比赛，加一个开球轮进球分数是否翻倍。
 所有以上都使用单选开关的样式，必要的时候可以调用MCP获取组件。

- **预览与保存区**
  - 配置预览
  - 保存为模板功能
  - 加载已有模板功能
  - 开始比赛按钮

#### 3.1.2 比赛进行中计分界面

- **选手状态区**
  - 动态显示当前选手、后序选手、前序选手
  - 当前击球选手高亮显示
  - 每个选手的累计得分
  - 每个选手的胜局数

- **台面状态区**
  - 剩余球可视化显示（以台球形式）
  - 当前最小号球标识
  - 是否处于让球状态的指示器

- **操作区**
  - 进球选择器（可多选）
  - 犯规按钮
  - 让球按钮（条件满足时显示）
  - 撤销上一步按钮
  - 结束当前局按钮

- **当前局记录区**
  - 以轮为单位显示进球、犯规、让球等事件
  - 连续进球视觉上显示为一组
  - 同时发生的进球和犯规视觉关联显示
  - 特殊事件（如黄金、小金等）突出显示

- **历史记录区**
  - 可折叠的历史局记录
  - 每局关键数据汇总（总分、胜者）
  - 展开可查看该局详细轮次记录

#### 3.1.3 比赛结束统计界面

- **比赛总览区**
  - 比赛总时长
  - 总局数
  - 最终比分与获胜者
  - 比赛模式与规则概述

- **选手统计区**
  - 每位选手的得分柱状图
  - 胜局数对比
  - 每位选手的特殊事件统计（黄金9、清台次数等）
  - 单局最高得分

- **数据分析区**
  - 进球效率分析
  - 各球对各选手的价值分析
  - 清台成功率分析
  - 让球决策分析

- **操作区**
  - 导出比赛记录按钮
  - 开始新比赛按钮
  - 使用相同配置再战按钮

### 3.2 最小化用户操作流程

#### 3.2.1 开球轮操作流程

1. 系统自动标识当前为开球轮
2. 用户仅需两个输入：
   - 选择进袋的球（可多选）
   - 标记是否犯规
3. 系统自动处理：
   - 计算黄金球或黑金球得分
   - 判断是否直接胜负（如黄金9）
   - 更新选手得分和台面状态
   - 决定是否换人或继续

#### 3.2.2 非开球轮操作流程

1. 常规击球情况：
   - 选择进袋的球（可多选）
   - 标记是否犯规
   - 系统自动计算得分和下一步行动

2. 让球情况处理：
   - 系统检测到让球条件时显示让球选项
   - 后序选手决定是否让球
   - 选择让球后系统自动调整选手顺序
   - 当前选手继续操作

### 3.3 数据记录结构细化

#### 3.3.1 回合记录优化

```typescript
interface TurnRecord {
  // 现有字段...
  lettingShot?: {
    isLetShot: boolean;            // 是否为让球
    originalPlayerOrder: number[]; // 让球前的选手顺序
    newPlayerOrder: number[];      // 让球后的选手顺序  
  };
  ballsBySettlement: {             // 按结算对象分组的球
    [settlementPlayerId: number]: number[]; // 与每个玩家结算的球列表
  };
  specialEvents: {                 // 特殊事件记录
    type: string;                  // 事件类型（黄金、小金等）
    ballsInvolved: number[];       // 相关球号
    scoreChange: Record<number, number>; // 各选手得分变化
  }[];
  continuationOf?: number;         // 若为连续击球，关联前一轮ID
}
```

#### 3.3.2 用户输入事件记录

```typescript
interface UserInputEvent {
  timestamp: Date;                // 输入时间戳
  turnId: number;                 // 所属轮次
  type: 'pocketBalls' | 'foul' | 'letShot'; // 输入类型
  data: {
    pocketedBalls?: number[];     // 进袋的球
    hasFoul?: boolean;            // 是否犯规
    isLetShot?: boolean;          // 是否让球
  };
}
```

## 4. 核心数据结构

### 4.1 比赛配置(MatchConfiguration)

```typescript
interface MatchConfiguration {
  type: 'frameLimit' | 'timeLimit' | 'scoreLimit' | 'manual';  // 比赛类型
  frameLimit?: number;        // 局数限制（适用于固定局数）
  timeLimit?: {               // 时间限制（适用于固定时间）
    type: 'duration' | 'endTime';
    duration?: number;        // 持续时间（分钟）
    endTime?: Date;           // 结束时间点
  };
  scoreLimit?: number;        // 分数限制（适用于固定分数）
  tiebreaker?: {              // 平局处理规则
    enabled: boolean;
    method: 'extraFrame' | 'highestScore' | 'suddenDeath';
  };
  gameConfiguration: GameConfiguration;  // 游戏规则配置
}
```

### 4.2 比赛记录(MatchRecord)

```typescript
interface MatchRecord {
  id: string;                 // 比赛ID
  title: string;              // 比赛标题
  configuration: MatchConfiguration;  // 比赛配置
  startTime: Date;            // 开始时间
  endTime?: Date;             // 结束时间
  players: PlayerRecord[];    // 参与选手
  frames: FrameRecord[];      // 所有局记录
  currentFrameId?: number;    // 当前局ID
  winner?: number;            // 胜者ID
  status: 'ongoing' | 'completed' | 'paused'; // 比赛状态
  stats: {                    // 比赛统计数据
    playerStats: Record<number, PlayerMatchStats>;  // 每位选手的统计
    frameStats: Record<number, FrameStats>;  // 每局的统计
    duration?: number;        // 比赛持续时间（分钟）
  };
}

interface PlayerMatchStats {
  playerId: number;
  totalScore: number;         // 总分
  framesWon: number;          // 获胜局数
  specialEvents: Record<string, number>;  // 特殊事件统计
  highestFrameScore: number;  // 单局最高分
  averageFrameScore: number;  // 平均每局得分
  consecutiveWins: number;    // 连胜局数
}
```

### 4.3 球设置(BallConfiguration)

```typescript
interface BallConfiguration {
  id: number;                 // 球号
  initialAttributes: {        // 初始属性
    scoringForPlayers: number[];  // 对哪些选手是得分球
    endingForPlayers: number[];   // 对哪些选手是结束球
    points: Record<number, number>; // 对每个选手的分值
    isSpecial: boolean;       // 是否为特殊球（如5号或9号）
    color: string;            // 球的颜色
  };
  currentAttributes?: {       // 当前属性（可以在比赛中变化）
    scoringForPlayers: number[];
    endingForPlayers: number[];
    points: Record<number, number>;
  };
}
```

### 4.4 游戏配置(GameConfiguration)

```typescript
interface GameConfiguration {
  totalBalls: number;         // 桌面总目标球数
  playerCount: number;        // 选手数量
  balls: BallConfiguration[]; // 所有球的配置
  specialRules: {
    allowConsecutiveShots: boolean; // 是否允许连续击球
    allowLettingShot: boolean;      // 是否允许让球
    slamModes: string[];           // 支持的满贯模式
    defaultSlamMode: string;       // 默认满贯模式
    ballAttributeChangeRules: BallAttributeChangeRule[]; // 球属性变化规则
  };
  scoringSystem: {
    foulPenalty: number;     // 犯规扣分
    bonusForSpecialEvents: Record<string, number>; // 特殊事件额外得分
  };
  breakRules: {                      // 开球规则
    mustHitLowest: boolean;          // 开球是否必须先撞击最小号球
    mustPocketBall?: boolean;        // 开球是否必须有球入袋
    mustDriveToRail?: number;        // 开球后至少多少颗球需要碰库边
    illegalBreakAction: 'foul' | 'opponentChooses'; // 违例开球的处理方式
    breakShotPoints?: 'normal' | 'double'; // 开球轮进球得分方式 ('double' 对应黄金/黑金规则, 仅3人以上生效)
  };
  foulRules: {
    foulPenaltyPoints: number;       // 犯规未进球时的罚分值 (配置项)
    foulPointsHandler: 'previousPlayer' | 'foulPool'; // 罚分处理方式：给上家或累计到犯规池
    scratchOnBreak?: 'ballInHandAnywhere' | 'ballInHandBehindHeadString'; // 开球犯规（如母球落袋）的处理
    threeConsecutiveFoulsLoss?: boolean; // 是否启用连三杆犯规判负规则
  };
  specialWinConditions: {            // 特殊胜利条件
    goldenBreakNineBall?: boolean;   // 是否启用黄金九（开球进九号球直接胜）
    blackNineBallLoss?: boolean;     // 是否启用黑九（开球犯规进九号球直接负）
    slamPoints?: number;             // 清台得分（小金/大金的基础分值）
    golden9Points?: number;          // 黄金9/黑9的特殊分值
  };
  advancedOptions?: {
    allowPushAfterBlock?: boolean;   // 遮挡球后是否允许让球（推杆）
    scoreBallDelay?: number;         // 得分球延迟结算颗数 (0表示不延迟)
    // ... 其他高级选项
  };
}
```

### 4.5 球属性变化规则(BallAttributeChangeRule)

```typescript
interface BallAttributeChangeRule {
  triggerCondition: {
    type: string;            // 触发类型，如"pocketBall", "foul", "consecutiveShots"等
    ballIds?: number[];      // 相关球号
    playerIds?: number[];    // 相关选手ID
    shotCount?: number;      // 连续击球次数
  };
  effect: {
    targetBalls: number[];   // 受影响的球
    changes: {
      addScoringForPlayers?: number[];
      removeScoringForPlayers?: number[];
      addEndingForPlayers?: number[];
      removeEndingForPlayers?: number[];
      changePoints?: Record<number, number>;
    };
  };
}
```

### 4.6 桌面状态(TableState)

```typescript
interface TableState {
  remainingBalls: number[];              // 剩余球号
  currentMinBall: number;                // 当前最小球
  ballStates: Record<number, BallState>; // 每个球的当前状态
  isObstructed: boolean;                 // 最小球是否被遮挡
  isLettingShot: boolean;                // 是否为让球状态
  originalPlayerOrder?: number[];        // 让球前的原始顺序
}

interface BallState {
  id: number;                            // 球号
  isOnTable: boolean;                    // 是否在桌面上
  owner?: number;                        // 进球的选手ID
  pocketedInTurn?: number;               // 在哪一轮进球
  pocketedInFrame?: number;              // 在哪一局进球
  currentAttributes: {                   // 当前属性
    scoringForPlayers: number[];
    endingForPlayers: number[];
    points: Record<number, number>;
  };
}
```

### 4.7 选手记录(PlayerRecord)

```typescript
interface PlayerRecord {
  id: number;
  name: string;
  position: string;                     // 动态的位置标识
  totalScore: number;                  // 总分
  framesWon: number;                   // 获胜局数
  specialStats: Record<string, number>; // 特殊事件统计（动态键）
  frameScores: Record<number, number>; // 每局得分
  turnHistory: number[];              // 历史轮IDs
  consecutiveShotCount: number;       // 连续击球次数
  consecutiveBalls: number[];         // 连续进球
}
```

### 4.8 游戏状态(GameState)

```typescript
interface GameState {
  config: GameConfiguration;       // 游戏配置
  matchConfig: MatchConfiguration; // 比赛配置
  matchId: string;                // 所属比赛ID
  players: PlayerRecord[];         // 所有选手
  playerIndices: {                // 动态的选手索引
    current: number;
    next: number[];               // 支持多个后续选手
    previous: number[];           // 支持多个前序选手
  };
  frameCount: number;             // 总局数
  currentFrameId: number;         // 当前局ID
  turnCount: number;              // 当前局轮数
  tableState: TableState;         // 桌面状态
  activeSlamMode: string;         // 当前满贯模式
  frames: FrameRecord[];          // 所有局记录
  currentTurn?: TurnRecord;       // 当前轮记录
  gameStartTime: Date;            // 游戏开始时间
  gameEndTime?: Date;             // 游戏结束时间
  ruleEngine: RuleEngine;         // 规则引擎
  matchStatus: 'ongoing' | 'completed' | 'paused'; // 比赛状态
  timeRemaining?: number;         // 剩余时间（适用于时间限制模式）
}
```

### 4.9 规则引擎(RuleEngine)

```typescript
interface RuleEngine {
  evaluateTurn: (turn: TurnRecord, state: GameState) => TurnEvaluationResult;
  determineTurnEnd: (state: GameState) => boolean;
  determineFrameEnd: (state: GameState) => boolean;
  determineMatchEnd: (state: GameState) => boolean; // 判断比赛是否结束
  determineNextPlayer: (state: GameState) => number;
  evaluateBallAttributeChanges: (event: GameEvent, state: GameState) => BallAttributeChange[];
  evaluateSpecialEvents: (turn: TurnRecord, state: GameState) => SpecialEvent[];
}
```

### 4.10 局记录(FrameRecord)

```typescript
interface FrameRecord {
  frameId: number;          // 局ID
  matchId: string;          // 所属比赛ID
  startTime: Date;          // 开始时间
  endTime?: Date;           // 结束时间
  players: PlayerInFrame[]; // 参与选手
  turns: TurnRecord[];      // 本局所有轮记录
  winner?: number;          // 胜者ID
  specialEvents: SpecialEvent[]; // 特殊事件记录
  finalBallState: Record<number, BallState>; // 最终球状态
}
```

### 4.11 轮记录(TurnRecord)

```typescript
interface TurnRecord {
  turnId: number;           // 轮ID
  frameId: number;          // 所属局ID
  matchId: string;          // 所属比赛ID
  playerId: number;         // 击球选手ID
  startTime: Date;          // 开始时间
  endTime?: Date;           // 结束时间
  pocketedBalls: number[];  // 进袋的球
  hasFoul: boolean;         // 是否犯规
  foulDetails?: FoulDetail; // 犯规详情
  scoreChange: number;      // 得分变化
  endReason: TurnEndReason; // 结束原因
  ballStateAfter: Record<number, BallState>; // 结束后球状态
  isConsecutive: boolean;   // 是否为连续击球状态
}
```

## 5. 界面设计

### 5.1 比赛配置界面

- 比赛类型选择（固定局数、固定时间、固定分数、手动结束）
- 根据比赛类型显示对应的设置选项
- 平局处理规则设置
- 游戏基本配置设置

### 5.2 游戏设置界面

- 选手数量选择（2-3人或更多）
- 总目标球数设置
- 每个球的初始属性配置（分值、颜色、特殊属性等）
- 规则配置（让球规则、连续击球规则、满贯模式等）
- 特殊事件触发条件和奖励设置

### 5.3 动态布局系统

- 根据选手数量自动调整界面布局
- 根据球数量动态生成球状态显示
- 根据配置的规则动态显示或隐藏特定操作按钮

### 5.4 游戏主界面

- 比赛信息显示（类型、进度、剩余时间/局数/分数等）
- 当前局数、轮数显示
- 当前选手信息与操作区
- 桌面球状态显示区
- 选手状态卡片区
- 历史记录与事件日志区

### 5.5 高级统计面板

- 比赛总体统计数据
- 每个球对各选手的价值分析
- 球属性动态变化的可视化展示
- 基于当前配置的胜率预测
- 选手表现趋势图

### 5.6 规则帮助系统

- 根据当前配置生成适配的规则说明
- 针对特定情况的规则解释和建议

### 5.7 界面设计优化

- **配置向导**：引导式配置流程，逐步完成各项设置
- **配置模板**：常用规则的预设模板，一键应用
- **配置验证**：实时验证配置是否有矛盾，并提供修正建议
- **可视化预览**：配置过程中动态预览比赛初始状态

### 5.8 比赛计分界面优化

- **简化操作区**：
  - 大按钮显示当前可能的操作（进球/犯规/让球）
  - 快速选择常见进球组合
  - 触摸优化的球选择器（移动端适配）
  
- **台面状态可视化**：
  - 3D效果显示剩余球在台面上的分布
  - 颜色编码显示每个球对各选手的价值
  - 最小球路径提示

- **选手状态卡片设计**：
  - 当前选手卡片放大突出
  - 选手间关系（当前/后序/前序）清晰标识
  - 实时得分和进球历史视觉化展示
  
- **实时规则提示**：
  - 情境化规则提示（如即将清台、可能的让球情况）
  - 特殊事件预告（"再进两球将完成小金"）

### 5.9 统计界面优化

- **互动式数据图表**：
  - 可调整时间范围的得分曲线
  - 选手表现对比雷达图
  - 进球分布热力图
  
- **成就系统**：
  - 自动识别并标记比赛中的高光时刻
  - 特殊成就解锁（如"三连清台"、"完美开局"）
  
- **数据导出选项**：
  - PDF比赛报告
  - 可共享的比赛回顾链接
  - 数据导出为CSV格式供进一步分析

## 6. 系统架构

### 6.1 模块化设计

- 核心游戏逻辑模块
- 比赛管理模块
- 规则引擎模块
- 用户界面模块
- 数据持久化模块
- 统计分析模块
- 计时器和事件调度模块

### 6.2 事件驱动机制

- 定义标准游戏事件（击球、进球、犯规等）
- 定义比赛事件（开始、暂停、结束等）
- 实现事件分发系统
- 允许规则和UI通过事件订阅来响应游戏变化

### 6.3 配置持久化

- 支持保存和加载自定义游戏配置
- 支持比赛状态的保存和恢复
- 版本控制机制确保向后兼容性

### 6.4 插件系统

- 支持通过插件扩展新规则
- 支持通过插件添加新的统计分析方法
- 支持通过插件定制UI组件
- 支持通过插件扩展比赛组织方式

## 7. 交互流程设计

### 7.1 错误防护机制

- **操作确认**：关键操作（如结束局）需二次确认
- **撤销/重做**：支持多步撤销，修正误操作
- **自动保存**：定期自动保存比赛状态，防止数据丢失
- **冲突检测**：检测不合理操作（如选择不存在的球）并阻止

### 7.2 反馈机制设计

- **视觉反馈**：所有操作有清晰的视觉反馈
- **音效反馈**：关键事件配有适当音效（可配置）
- **成功/失败状态**：清晰区分操作成功与失败状态
- **规则提示**：违反规则时给出具体提示，帮助用户理解

## 8. 未来扩展计划

- 多设备同步支持
- 在线对战与记分板共享
- AI辅助分析与建议
- 比赛回放与分析功能
- 移动端适配与优化
- 比赛排名和积分系统
- 比赛日程和选手管理

## 9. 实现路线图

### 9.1 第一阶段：核心功能实现

- 基础数据结构设计与实现
- 核心游戏逻辑实现
- 基本UI界面实现
- 简单比赛管理功能实现

### 9.2 第二阶段：扩展性增强

- 可配置规则引擎实现
- 动态UI系统实现
- 数据持久化实现
- 完善比赛管理系统

### 9.3 第三阶段：用户体验优化

- 高级统计与分析功能
- 规则帮助系统实现
- 界面美化与交互优化
- 比赛计时和提醒功能

### 9.4 第四阶段：插件系统与扩展功能

- 插件系统架构设计与实现
- 示例插件开发
- 社区插件支持
- 高级比赛组织和管理功能
