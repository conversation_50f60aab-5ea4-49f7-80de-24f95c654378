import { MatchConfig } from '../types/MatchConfig';

const TEMPLATES_KEY = 'billiards_templates';

export interface Template {
  id: string;
  name: string;
  createdAt: number;
  config: MatchConfig;
}

export const TemplateService = {
  // 获取所有模板
  getAllTemplates: (): Template[] => {
    const templatesJson = localStorage.getItem(TEMPLATES_KEY);
    if (!templatesJson) {
      return [];
    }
    try {
      return JSON.parse(templatesJson);
    } catch (e) {
      console.error('Failed to parse templates', e);
      return [];
    }
  },

  // 保存模板
  saveTemplate: (name: string, config: MatchConfig): Template => {
    const templates = TemplateService.getAllTemplates();

    // 创建配置的深拷贝
    const configCopy = JSON.parse(JSON.stringify(config));

    // 移除可变信息
    if (configCopy.basicInfo) {
      configCopy.basicInfo.name = ''; // 清除比赛名称
      configCopy.basicInfo.date = ''; // 清除比赛日期
      configCopy.basicInfo.location = ''; // 清除比赛地点
    }

    // 完全移除选手名称，而不是设置默认值
    if (configCopy.playerConfigs) {
      configCopy.playerConfigs.forEach((player: any) => {
        // 删除选手名称属性，而不是设置默认值
        delete player.playerName;
        delete player.name;
      });
    }

    const newTemplate: Template = {
      id: Date.now().toString(),
      name,
      createdAt: Date.now(),
      config: configCopy
    };

    templates.push(newTemplate);
    localStorage.setItem(TEMPLATES_KEY, JSON.stringify(templates));

    return newTemplate;
  },

  // 删除模板
  deleteTemplate: (id: string): boolean => {
    const templates = TemplateService.getAllTemplates();
    const filteredTemplates = templates.filter(t => t.id !== id);

    if (filteredTemplates.length === templates.length) {
      return false; // 没有找到要删除的模板
    }

    localStorage.setItem(TEMPLATES_KEY, JSON.stringify(filteredTemplates));
    return true;
  },

  // 获取单个模板
  getTemplate: (id: string): Template | null => {
    const templates = TemplateService.getAllTemplates();
    return templates.find(t => t.id === id) || null;
  }
};
