import React, { useState, useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Container,
  Box,
  Button,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Paper,
  Alert,
  List,
  ListItem,
  ListItemText,
  Checkbox,
  FormControlLabel,
  Chip
} from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
// import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline'; // 暂时不使用
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import SkipNextIcon from '@mui/icons-material/SkipNext';
import HomeIcon from '@mui/icons-material/Home';
import { MatchBasicInfo } from '../components/match-config/MatchBasicInfoForm';
import { PlayerBallConfig } from '../types/playerConfig';
import { RuleEngine, GameState } from '../utils/RuleEngine';
import { RuleConfig } from '../components/match-config/RuleConfigForm';
import { SpecialEventManager, SpecialEventInfo } from '../utils/SpecialEventManager';

// 定义击球结果类型
type ShotResult = 'pot' | 'miss' | 'foul' | 'concede';
type PlayerRole = 'current' | 'next' | 'previous';

// 定义进球信息
interface PottedBall {
  id: number;
  points: number;
}

// 定义轮次信息
interface Turn {
  playerId: number;
  isBreakShot: boolean;
  shots: {
    result: ShotResult;
    pottedBalls?: PottedBall[];
    foulPoints?: number;
  }[];
  scores?: Record<number, number>; // 添加分数记录，用于跟踪每个回合的分数变化
}

// 定义局信息
interface Frame {
  frameNumber: number;
  turns: Turn[];
  scores: Record<number, number>; // playerId -> score
  winner?: number; // playerId of winner
}

// 定义比赛状态
interface MatchState {
  currentFrame: Frame;
  frames: Frame[];
  currentTurn: Turn;
  players: {
    id: number;
    name: string;
    role: PlayerRole;
  }[];
  remainingBalls: number[];
  totalScores: Record<number, number>; // playerId -> total score
}

const ScoringPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { basicInfo, playerConfigs, ruleConfig } = location.state as {
    basicInfo: MatchBasicInfo;
    playerConfigs: PlayerBallConfig[];
    ruleConfig: RuleConfig;
  };

  // 状态
  const [matchState, setMatchState] = useState<MatchState | null>(null);
  const [currentGameState, setCurrentGameState] = useState<GameState | null>(null);
  const [ruleEngine, setRuleEngine] = useState<RuleEngine | null>(null);
  const [logEntries, setLogEntries] = useState<string[]>([]);
  const [pendingNextFrameState, setPendingNextFrameState] = useState<GameState | null>(null);

  // 新的计分操作状态
  const [selectedBalls, setSelectedBalls] = useState<number[]>([]);
  const [isFoulChecked, setIsFoulChecked] = useState(false);
  const [currentAction, setCurrentAction] = useState<ShotResult | null>(null);
  // 使用规则配置中的犯规分值，而不是让用户选择
  const getFoulPoints = useCallback(() => {
    return ruleConfig?.foulRules.foulPenaltyPoints || 3; // 默认值为3
  }, [ruleConfig]);

  // 对话框状态
  const [shotDialogOpen, setShotDialogOpen] = useState(false);
  const [frameEndDialogOpen, setFrameEndDialogOpen] = useState(false);
  const [matchEndDialogOpen, setMatchEndDialogOpen] = useState(false);
  const [frameWinner, setFrameWinner] = useState<number | null>(null);
  const [matchWinner, setMatchWinner] = useState<number | null>(null); // 暂未使用，但保留供未来扩展
  // 错误消息状态，用于显示错误提示
  // 使用状态变量而不是引用变量，避免未使用变量的警告
  const [, setErrorMessage] = useState<string | null>(null);
  // 当前特殊事件，如黑金5、黄金5等
  const [currentSpecialEvent, setCurrentSpecialEvent] = useState<string | undefined>(undefined);
  // 触发特殊事件的选手 ID，用于跟踪特殊事件的触发者，确保特殊事件的处理只发生一次
  const [specialEventPlayerId, setSpecialEventPlayerId] = useState<number | null>(null);
  // 局结束对话框中显示的特殊事件
  const [frameEndSpecialEvent, setFrameEndSpecialEvent] = useState<string | undefined>(undefined);

  // 检查当前玩家是否是特殊事件的触发者
  const isSpecialEventTrigger = useCallback((playerId: number) => {
    return specialEventPlayerId === playerId;
  }, [specialEventPlayerId]);

  // 使用SpecialEventManager统一处理特殊事件
  const [currentSpecialEventInfo, setCurrentSpecialEventInfo] = useState<SpecialEventInfo>({
    type: null,
    triggerId: null
  });

  // 特殊事件处理相关逻辑集中到这里
  useEffect(() => {
    if (matchState?.players && logEntries.length > 0) {
      const specialEventInfo = SpecialEventManager.detectSpecialEventFromLogs(
        logEntries,
        matchState.players.map(p => ({ id: p.id, name: p.name }))
      );

      setCurrentSpecialEventInfo(specialEventInfo);

      // 保持向后兼容
      if (specialEventInfo.type) {
        setCurrentSpecialEvent(specialEventInfo.type === 'blackGolden5' ? '黑金5' :
                              specialEventInfo.type === 'golden5' ? '黄金5' :
                              specialEventInfo.type === 'normalBlack5' ? '普通黑5' : undefined);
        setSpecialEventPlayerId(specialEventInfo.triggerId);
      } else {
        setCurrentSpecialEvent(undefined);
        setSpecialEventPlayerId(null);
      }
    }
  }, [logEntries, matchState?.players]);

  // Helper function to calculate total scores
  const calculateTotalScores = useCallback((frames: Frame[], players: PlayerBallConfig[]): Record<number, number> => {
    // 初始化总分为0
    const totalScores = Object.fromEntries(players.map(p => [p.playerId, 0]));

    // 遍历每一局，累加分数
    frames.forEach((frame) => {
      // This is a simple sum, might need adjustment based on win/loss points per frame
      for (const playerId in frame.scores) {
        if (Object.prototype.hasOwnProperty.call(frame.scores, playerId)) {
          const oldScore = totalScores[playerId] || 0;
          const frameScore = frame.scores[playerId];
          totalScores[playerId] = oldScore + frameScore;
        }
      }
    });

    return totalScores;
  }, []);



  // 更新分数辅助函数
  const updateScores = useCallback((newScores: Record<number, number>) => {
    setMatchState(prev => {
      if (!prev) return null;

      // 更新当前局分数
      const updatedFrameScores = { ...prev.currentFrame.scores };

      // 合并新分数
      for (const [playerId, score] of Object.entries(newScores)) {
        const id = Number(playerId);
        updatedFrameScores[id] = (updatedFrameScores[id] || 0) + score;
      }

      // 更新当前局
      const updatedFrame: Frame = {
        ...prev.currentFrame,
        scores: updatedFrameScores
      };

      // 计算总分
      const updatedTotalScores = calculateTotalScores([...prev.frames, updatedFrame], playerConfigs);

      return {
        ...prev,
        currentFrame: updatedFrame,
        totalScores: updatedTotalScores
      };
    });
  }, [calculateTotalScores, playerConfigs]);

  // 工具函数 - 定义移到 logScoreChanges 之前
  // 添加去重机制，避免重复日志
  const addLogEntry = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString('zh-CN', { hour12: false });
    const fullMessage = `[${timestamp}] ${message}`;

    // 检查是否与最后一条日志相同
    setLogEntries(prev => {
      // 如果最后一条日志与当前日志相同，则不添加
      if (prev.length > 0 && prev[prev.length - 1].endsWith(message)) {
        return prev;
      }

      // 如果是新的日志，则添加
      return [...prev, fullMessage];
    });
  }, []);

  // Helper function to log score changes
  const logScoreChanges = useCallback((scores: Record<number, number>, _previousScores: Record<number, number> | undefined, getName: (id: number) => string) => {
    let changesDesc = "";

    // 直接使用 scores 中的分数变化
    Object.entries(scores).forEach(([playerIdStr, score]) => {
      const playerId = Number(playerIdStr);
      if (isNaN(playerId)) return;

      if (score !== 0) {
        changesDesc += `${getName(playerId)} ${score > 0 ? '+' : ''}${score}分; `;
      }
    });

    if (changesDesc) {
      // 记录分数变化日志
      addLogEntry(changesDesc.trim());
    }
  }, [addLogEntry]);

  // 工具函数
  const getPlayerName = useCallback((playerId: number): string => {
    return playerConfigs.find(p => p.playerId === playerId)?.playerName || `选手 ${playerId}`;
  }, [playerConfigs]);

  // 初始化 Effect
  useEffect(() => {
    if (playerConfigs && ruleConfig) {
      try {
        // Fix: Correct constructor arguments
        const engine = new RuleEngine(ruleConfig, playerConfigs, playerConfigs.length);
        setRuleEngine(engine);

        // 初始化第一个 GameState
        const initialPlayerId = playerConfigs[0]?.playerId;
        const nextPlayerId = playerConfigs[1]?.playerId; // Assuming at least 2 players initially
        const previousPlayerId = playerConfigs[playerConfigs.length -1]?.playerId;
        const initialRemainingBalls = Array.from({ length: basicInfo.totalBalls }, (_, i) => i + 1);
        const initialGameState: GameState = {
          currentPlayerId: initialPlayerId,
          nextPlayerId: nextPlayerId,
          previousPlayerId: previousPlayerId,
          remainingBalls: initialRemainingBalls,
          currentMinBall: 1,
          isBreakShot: true,
          isObstructed: false,
          canLetShot: false, // Initially false
          foulPool: 0, // Initialize foul pool
          // Fix: Add missing playerCount property
          playerCount: playerConfigs.length
        };
        setCurrentGameState(initialGameState);

        // 初始化 MatchState
        const initialPlayers = playerConfigs.map((config, index) => ({
          id: config.playerId,
          name: config.playerName,
          role: index === 0 ? 'current' as const :
                index === 1 ? 'next' as const :
                'previous' as const
        }));

        const initialScores = Object.fromEntries(playerConfigs.map(p => [p.playerId, 0]));

        const initialFrame: Frame = {
          frameNumber: 1,
          turns: [],
          scores: { ...initialScores }
        };

        const initialTurn: Turn = {
          playerId: initialPlayerId,
          isBreakShot: true,
          shots: []
        };

        const initialMatchState: MatchState = {
          currentFrame: initialFrame,
          frames: [],
          currentTurn: initialTurn,
          players: initialPlayers,
          remainingBalls: initialRemainingBalls,
          totalScores: { ...initialScores }
        };
        setMatchState(initialMatchState);

        // 合并日志，避免重复
        // 清空日志，确保不会重复
        setLogEntries([]);
        addLogEntry(`比赛开始！模式: ${basicInfo.mode}, ${playerConfigs.length}位选手。`);
        addLogEntry(`第 1 局开始。开球轮，轮到选手 ${getPlayerName(initialPlayerId)} (${initialPlayerId}) 击球。`);

      } catch (error) {
        console.error("初始化比赛引擎或状态时出错:", error);
        setErrorMessage("无法初始化比赛，请检查配置。错误: " + (error instanceof Error ? error.message : String(error)));
      }
    } else {
      console.error("缺少比赛配置信息，无法初始化页面。");
      setErrorMessage("缺少比赛配置信息，无法导航到计分页面。请返回重试。");
    }
  }, [basicInfo, playerConfigs, ruleConfig, navigate, addLogEntry, getPlayerName]);

  // 事件处理 - 已被直接处理函数替代
  // 保留以下代码供参考
  /*
  const handleShot = (result: ShotResult) => {
    setCurrentAction(result);
    setSelectedBalls([]);
    setFoulPoints(0);
    setShotDialogOpen(true);
  };
  */

  // 直接处理进球操作
  const handleDirectPot = useCallback((balls: number[], hasFoul: boolean) => {
    if (!ruleEngine || !currentGameState || !matchState) {
      console.error("引擎或状态未初始化，无法处理击球。");
      setErrorMessage("发生内部错误，无法处理击球。请刷新或返回重试。");
      return;
    }

    // 使用规则配置中的犯规分值，不再需要用户选择

    try {
      // 记录进球信息
      // 检查是否有分数球
      const scoringBalls = balls.filter(ballId => {
        const ballConfig = playerConfigs.find(p => p.playerId === currentGameState.currentPlayerId)?.balls.find(b => b.id === ballId);
        return ballConfig && ballConfig.points > 0;
      });

      if (hasFoul && scoringBalls.length > 0) {
        // 犯规进分数球，记录为黑n
        // 判断是否是黑金5事件
        if (currentGameState.isBreakShot && balls.includes(5)) {
          addLogEntry(`选手 ${getPlayerName(currentGameState.currentPlayerId)} 进了 ${balls.join(', ')} 号球，同时犯规。（黑金5)`);
          console.log('黑金5事件日志记录');
        } else {
          addLogEntry(`选手 ${getPlayerName(currentGameState.currentPlayerId)} 进了 ${balls.join(', ')} 号球，同时犯规。（黑${scoringBalls.join(', ')})`);
        }
      } else if (balls.length > 0) {
        // 普通进球或普通犯规进球
        addLogEntry(`选手 ${getPlayerName(currentGameState.currentPlayerId)} 进了 ${balls.join(', ')} 号球${hasFoul ? '，同时犯规' : ''}。`);
      } else if (hasFoul) {
        // 纯犯规，没有进球
        addLogEntry(`选手 ${getPlayerName(currentGameState.currentPlayerId)} 犯规。`);
      }

      // 评估回合结果
      const result = ruleEngine.evaluateTurn(currentGameState, balls, hasFoul);
      const nextState = result.nextState;

      // 更新特殊事件状态
      let specialEvent = result.specialEvent;

      // 判断特殊事件类型
      const isBreakShotCurrent = currentGameState.isBreakShot;

      // 处理特殊事件
      if (isBreakShotCurrent && balls.length === 1 && balls[0] === 5 && !hasFoul && specialEvent === '黄金开球') {
        specialEvent = '黄金5';
      } else if (isBreakShotCurrent && balls.length === 1 && balls[0] === 5 && hasFoul) {
        specialEvent = '黑金5';
      } else if (isBreakShotCurrent && balls.length === 1 && balls[0] === 9 && hasFoul && specialEvent === '黑9') {
        specialEvent = '黄金黑9';
      }

      setCurrentSpecialEvent(specialEvent);
      setSpecialEventPlayerId(currentGameState.currentPlayerId);

      // 如果是普通犯规，记录犯规罚分
      // 注意：黑金5事件不应该走普通犯规的处理逻辑
      if (specialEvent === '普通犯规') {
        // 获取犯规罚分
        const foulPenalty = ruleConfig?.foulRules?.foulPenaltyPoints || 0;
        const foulHandler = ruleConfig?.foulRules?.foulPointsHandler;

        // 生成犯规罚分日志
        let foulLog = `选手 ${getPlayerName(currentGameState.currentPlayerId)} 犯规，扣除 ${foulPenalty}分`;

        // 根据犯规处理方式添加得分信息
        if (foulHandler === 'opponent') {
          // 平分给所有对手
          const opponents = playerConfigs.filter(p => p.playerId !== currentGameState.currentPlayerId);
          if (opponents.length > 0) {
            const pointsPerOpponent = Math.floor(foulPenalty / opponents.length);
            foulLog += `; 其他选手各得 ${pointsPerOpponent}分`;
          }
        } else if (foulHandler === 'nextPlayer' && currentGameState.nextPlayerId !== undefined) {
          foulLog += `; 选手 ${getPlayerName(currentGameState.nextPlayerId)} 得 ${foulPenalty}分`;
        }

        // 记录犯规罚分日志
        addLogEntry(foulLog);
      }

      // 黑金5事件特殊处理
      if (specialEvent === '黑金5') {
        // 获取球的分值
        const ball5Config = playerConfigs.find(p => p.playerId === currentGameState.currentPlayerId)?.balls.find(b => b.id === 5);
        const ball5Points = ball5Config?.points || 0;

        // 生成黑金5事件日志
        let blackGolden5Log = `特殊事件: 黑金5! 选手 ${getPlayerName(currentGameState.currentPlayerId)} 开球犯规进5号球，扣除 ${ball5Points * 2} 分`;

        // 其他选手分别得分
        const opponents = playerConfigs.filter(p => p.playerId !== currentGameState.currentPlayerId);
        if (opponents.length > 0) {
          const pointsPerOpponent = ball5Points;
          blackGolden5Log += `; 其他选手各得 ${pointsPerOpponent} 分`;
        }

        // 记录黑金5事件日志
        addLogEntry(blackGolden5Log);
      }

      // 更新分数
      updateScores(result.scores);

      // 记录得分变化 - 只有当分数变化时才记录
      const hasScoreChanges = Object.values(result.scores).some(score => score !== 0);
      if (hasScoreChanges) {
        logScoreChanges(result.scores, matchState.currentFrame.scores, getPlayerName);
      }

      // 记录当前回合的分数变化和进球信息，用于后续计算总分和显示进球记录
      setMatchState(prev => {
        if (!prev) return null;

        // 创建进球记录
        // 判断是否是特殊事件
        // 注意：我们已经在下面的判断中直接使用 specialEvent === '黑金5' 和 specialEvent === '黄金5'

        // 如果是特殊事件，只在触发者的回合中创建进球记录
        let pottedBalls: { id: number; points: number }[] = [];

        // 黑金5事件特殊处理
        if (specialEvent === '黑金5') {
          console.log('创建黑金5事件的进球记录，当前选手ID:', currentGameState.currentPlayerId);

          // 如果是触发者，只创建5号球的进球记录
          // 判断是否是触发者：当前选手必须是开球者，并且进了5号球
          const isTrigger = currentGameState.isBreakShot && balls.includes(5) && hasFoul;
          // 如果是黑金5事件，且当前选手是触发者，只创建5号球的进球记录
          if (isTrigger) {
            console.log('是黑金5触发者，创建5号球记录');
            // 只添加5号球
            const ballConfig = playerConfigs.find(p => p.playerId === currentGameState.currentPlayerId)?.balls.find(b => b.id === 5);
            pottedBalls = [{
              id: 5,
              points: ballConfig?.points || 0
            }];

            // 记录触发者ID
            if (!isSpecialEventTrigger(currentGameState.currentPlayerId)) {
              setSpecialEventPlayerId(currentGameState.currentPlayerId);
            }
          } else {
            // 如果不是触发者，不创建任何进球记录
            console.log('不是黑金5触发者，不创建进球记录');
            pottedBalls = [];
          }
        }
        // 黄金5事件特殊处理
        else if (specialEvent === '黄金5') {
          console.log('创建黄金5事件的进球记录，当剋选手ID:', currentGameState.currentPlayerId);

          // 如果是触发者，只创建5号球的进球记录
          // 判断是否是触发者：当剋选手必须是开球者，并且进了5号球，且没有犯规
          const isTrigger = currentGameState.isBreakShot && balls.includes(5) && !hasFoul;

          if (isTrigger) {
            console.log('是黄金5触发者，创建5号球记录');
            // 只添加5号球
            const ballConfig = playerConfigs.find(p => p.playerId === currentGameState.currentPlayerId)?.balls.find(b => b.id === 5);
            pottedBalls = [{
              id: 5,
              points: ballConfig?.points || 0
            }];

            // 记录触发者ID
            if (!isSpecialEventTrigger(currentGameState.currentPlayerId)) {
              setSpecialEventPlayerId(currentGameState.currentPlayerId);
            }
          } else {
            // 如果不是触发者，不创建任何进球记录
            console.log('不是黄金5触发者，不创建进球记录');
            pottedBalls = [];
          }
        }
        // 如果不是特殊事件，正常创建进球记录
        else {
          pottedBalls = balls.map(ballId => {
            // 获取球的分值
            const ballConfig = playerConfigs.find(p => p.playerId === currentGameState.currentPlayerId)?.balls.find(b => b.id === ballId);
            return {
              id: ballId,
              points: ballConfig?.points || 0
            };
          });
        }

        // 创建新的shot对象
        const newShot = {
          result: hasFoul ? 'foul' as const : 'pot' as const,
          pottedBalls: pottedBalls,
          foulPoints: hasFoul ? getFoulPoints() : undefined
        };

        // 创建新的回合对象，包含分数变化和进球信息
        const updatedTurn: Turn = {
          ...prev.currentTurn,
          scores: { ...result.scores }, // 保存当前回合的分数变化
          shots: [...prev.currentTurn.shots, newShot] // 添加进球记录
        };

        console.log('更新回合信息:', updatedTurn);

        // 更新当前局的回合记录
        const updatedFrame: Frame = {
          ...prev.currentFrame,
          turns: [...prev.currentFrame.turns, updatedTurn]
        };

        return {
          ...prev,
          currentFrame: updatedFrame,
          currentTurn: {
            playerId: nextState.currentPlayerId,
            isBreakShot: nextState.isBreakShot,
            shots: []
          }
        };
      });

      // 判断是否继续当前回合
      const continueTurn = !result.hasFoul && result.pocketedBalls.length > 0 && nextState.remainingBalls.length > 0;

      // 更新游戏状态
      setCurrentGameState(nextState);

      // 检查局结束
      // 注意：黄金5和黑金5不应该导致局结束，它们只是特殊计分事件

      // 判断局结束条件时会用到特殊事件标记

      // 处理特殊事件的局结束逻辑
      const isBreakShotHere = currentGameState.isBreakShot;
      const hasGolden5 = isBreakShotHere && balls.length === 1 && balls[0] === 5 && !hasFoul;
      const hasBlack5 = isBreakShotHere && balls.length === 1 && balls[0] === 5 && hasFoul;
      const hasNormalBlack5 = !isBreakShotHere && balls.includes(5) && hasFoul;
      const hasGoldenBlack9 = isBreakShotHere && balls.length === 1 && balls[0] === 9 && hasFoul;

      // 如果是黄金5，记录特殊事件信息，但不结束局
      if (hasGolden5 || (isBreakShotHere && balls.includes(5) && !hasFoul)) {
        addLogEntry('特殊事件: 黄金5，只计分不结束局');
        // 强制设置不结束局
        nextState.isFrameOver = false;
      } else if (hasBlack5 || (isBreakShotHere && balls.includes(5) && hasFoul)) {
        // 获取5号球的分值
        const ball5Points = playerConfigs.find(p => p.playerId === currentGameState.currentPlayerId)?.balls.find(b => b.id === 5)?.points || 0;

        // 生成黑金5分数变化的详细日志
        let blackGolden5Log = `特殊事件: 黑金5，只计分不结束局。`;
        blackGolden5Log += `选手 ${getPlayerName(currentGameState.currentPlayerId)} 扣除 ${ball5Points * 2}分; `;

        // 添加其他选手得分信息
        playerConfigs.forEach(p => {
          if (p.playerId !== currentGameState.currentPlayerId) {
            blackGolden5Log += `选手 ${getPlayerName(p.playerId)} 得 ${ball5Points}分; `;
          }
        });

        // 记录黑金5特殊事件日志
        addLogEntry(blackGolden5Log.trim());

        // 强制设置不结束局
        nextState.isFrameOver = false;
      } else if (hasGoldenBlack9) {
        // 更新日志中的特殊事件名称
        addLogEntry('特殊事件: 黄金黑9，开球轮犯规打进9号球');
      }

      // 判断局结束条件，排除黄金5和黑金5事件
      // 如果是黄金5、黑金5或普通黑5事件，则强制设置为不结束局
      let isFrameOver = false;

      // 使用已经定义的特殊事件变量
      const isGolden5Event = hasGolden5 || (isBreakShotHere && balls.includes(5) && !hasFoul);
      const isBlack5Event = hasBlack5 || (isBreakShotHere && balls.includes(5) && hasFoul);
      const isNormalBlack5Event = hasNormalBlack5;

      if (isGolden5Event || isBlack5Event || isNormalBlack5Event) {
        // 强制设置为不结束局
        isFrameOver = false;
        // 如果规则引擎返回的 nextState.isFrameOver 为 true，则将其覆盖为 false
        nextState.isFrameOver = false;

        // 如果是普通黑5事件，记录日志信息并手动计算得分
        if (isNormalBlack5Event) {
          // 设置特殊事件为普通黑5，但在局结束时不显示
          setCurrentSpecialEvent('普通黑5');

          // 获取5号球的分值
          const ball5Points = playerConfigs.find(p => p.playerId === currentGameState.currentPlayerId)?.balls.find(b => b.id === 5)?.points || 0;

          // 生成普通黑5分数变化的详细日志
          let normalBlack5Log = `特殊事件: 普通黑5，只计分不结束局。`;
          normalBlack5Log += `选手 ${getPlayerName(currentGameState.currentPlayerId)} 扣除 ${ball5Points}分; `;
          normalBlack5Log += `选手 ${getPlayerName(currentGameState.previousPlayerId)} 得 ${ball5Points}分; `;

          // 记录普通黑5特殊事件日志
          addLogEntry(normalBlack5Log.trim());
        }
      } else {
        // 对于非黄金5、黑金5和普通黑5事件，正常判断局结束条件
        isFrameOver = nextState.remainingBalls.length === 0 ||
                      result.specialEvent === '大金' ||
                      result.specialEvent === '黑大金' ||
                      result.specialEvent === '小金' ||
                      result.specialEvent === '黑小金' ||
                      result.specialEvent === '黄金9' ||
                      result.specialEvent === '黑9' ||
                      result.specialEvent === '黄金黑9' ||
                      (balls && balls.includes(9)) || // 如果进了9号球，局结束
                      nextState.isFrameOver === true;

        // 确保黄金5和黑金5事件不会导致局结束
        if (result.specialEvent === '黄金5' || result.specialEvent === '黑金5' ||
            result.specialEvent === '黄金开球' && balls.includes(5) ||
            result.specialEvent === '黑金开球' && balls.includes(5)) {
          isFrameOver = false;
          nextState.isFrameOver = false;
        }
      }

      // 计算每个选手赢的局数
      const framesWon: Record<number, number> = {};
      matchState.players.forEach(player => {
        framesWon[player.id] = matchState.frames
          .filter(frame => frame.winner === player.id)
          .length;
      });

      // 检查比赛是否结束
      const matchEndStatus = ruleEngine.checkMatchEnd(
        matchState.totalScores,
        matchState.frames.length + 1, // 包括当前局
        framesWon
      );

      const isMatchOver = matchEndStatus.isEnd;
      if (isMatchOver) {
        setMatchWinner(matchEndStatus.winnerId || null);
        if (matchEndStatus.reason) {
          addLogEntry(`比赛结束！${matchEndStatus.reason}`);
        }
        if (matchEndStatus.winnerId !== undefined) {
          addLogEntry(`获胜者: ${getPlayerName(matchEndStatus.winnerId)}`);
        } else {
          addLogEntry('比赛结果: 平局');
        }

        // 打开比赛结束对话框
        setMatchEndDialogOpen(true);
      }

      if (isFrameOver) {
        // 处理局结束逻辑
        // 在这里直接处理局结束逻辑，避免依赖循环
        // 确定局的获胜者
        let winnerId: number | null = null;

        // 如果进了9号球，判断获胜者
        if (balls && balls.includes(9)) {
          if (hasFoul) {
            // 犯规进9号球，前序选手获胜
            winnerId = currentGameState.previousPlayerId;
            console.log(`选手 ${currentGameState.currentPlayerId} 犯规进9号球，选手 ${winnerId} 获胜`);
            // 设置特殊事件为“赢得本局”
            setCurrentSpecialEvent('赢得本局');
          } else {
            // 合法进9号球，当前选手获胜
            winnerId = currentGameState.currentPlayerId;
            console.log(`选手 ${winnerId} 合法进9号球获胜`);
            // 设置特殊事件为“赢得本局”
            setCurrentSpecialEvent('赢得本局');
          }
          // 输出调试日志
          console.log('当前选手:', currentGameState.currentPlayerId);
          console.log('前序选手:', currentGameState.previousPlayerId);
          console.log('后序选手:', currentGameState.nextPlayerId);
        } else if (result.specialEvent) {
          // 根据特殊事件确定获胜者
          if (result.specialEvent === '大金' || result.specialEvent === '黄金9' || result.specialEvent === '小金') {
            // 当前选手获胜
            winnerId = currentGameState.currentPlayerId;
          } else if (result.specialEvent === '黑小金' || result.specialEvent === '黑9') {
            // 前序选手或后序选手获胜
            winnerId = result.specialEvent === '黑小金' ? currentGameState.previousPlayerId : currentGameState.nextPlayerId;
          }
        }
        setFrameWinner(winnerId);

        // 处理局结束时的分数
        // 对于所有情况，都需要将当前回合的分数变化添加到当前局的累计分数中
        let finalFrameScores: Record<number, number> = {};

        // 对于大金、黑大金、黄金开球、黑金开球等特殊事件，直接使用 result.scores 作为本局最终得分
        // 这是因为这些特殊事件会直接结束本局，不需要累加到之前的分数
        if (result.specialEvent === '大金' ||
            result.specialEvent === '黑大金' ||
            result.specialEvent === '黄金开球' ||
            result.specialEvent === '黑金开球') {
            // 直接使用 result.scores 作为本局最终得分
            for (const [playerId, score] of Object.entries(result.scores)) {
                const id = Number(playerId);
                finalFrameScores[id] = score;
            }

            // 确保所有选手都有分数记录
            for (const player of playerConfigs) {
                if (finalFrameScores[player.playerId] === undefined) {
                    finalFrameScores[player.playerId] = 0;
                }
            }

            // 特殊事件日志记录
            console.log(`特殊事件: ${result.specialEvent}, 分数: `, finalFrameScores);
        } else {
            // 对于普通情况，我们需要手动计算最终分数
            // 由于可能存在重复计算的问题，我们将重新计算整个局的分数

            // 首先，获取所有回合的分数变化
            console.log('重新计算局分数 - 当前局所有回合:', matchState.currentFrame.turns);

            // 初始化分数为0
            finalFrameScores = Object.fromEntries(playerConfigs.map(p => [p.playerId, 0]));

            // 计算最终分数 - 使用累计的回合分数
            console.log('计算最终分数 - 使用累计的回合分数');

            // 初始化分数为0
            finalFrameScores = Object.fromEntries(playerConfigs.map(p => [p.playerId, 0]));

            // 累加所有回合的分数变化
            matchState.currentFrame.turns.forEach((turn, index) => {
                if (turn.scores) {
                    console.log(`处理第 ${index + 1} 回合分数:`, turn.scores);

                    // 累加每个选手的分数
                    for (const playerId in turn.scores) {
                        if (Object.prototype.hasOwnProperty.call(turn.scores, playerId)) {
                            const score = turn.scores[playerId];
                            const numPlayerId = Number(playerId);
                            finalFrameScores[numPlayerId] = (finalFrameScores[numPlayerId] || 0) + score;
                            console.log(`选手 ${playerId}: ${finalFrameScores[numPlayerId] - score} + ${score} = ${finalFrameScores[numPlayerId]}`);
                        }
                    }
                }
            });

            // 添加当前回合的分数变化（如果当前回合的分数变化尚未记录到turns中）
            // 通常情况下，当前回合的分数变化已经记录到了turns中，所以这部分代码可能不会执行
            // 但为了确保不遗漏任何分数变化，我们仍然保留这部分代码
            if (result.scores) {
                console.log('检查是否需要添加当前回合分数变化:', result.scores);

                // 检查当前回合的分数变化是否已经记录到了turns中
                const lastTurn = matchState.currentFrame.turns[matchState.currentFrame.turns.length - 1];
                const needToAddCurrentTurnScores = !lastTurn || !lastTurn.scores ||
                    Object.keys(lastTurn.scores).some(playerId => {
                        const numPlayerId = Number(playerId);
                        return lastTurn.scores![numPlayerId] !== result.scores[numPlayerId];
                    });

                if (needToAddCurrentTurnScores) {
                    console.log('添加当前回合分数变化:', result.scores);

                    for (const playerId in result.scores) {
                        if (Object.prototype.hasOwnProperty.call(result.scores, playerId)) {
                            const score = result.scores[playerId];
                            const numPlayerId = Number(playerId);
                            finalFrameScores[numPlayerId] = (finalFrameScores[numPlayerId] || 0) + score;
                            console.log(`选手 ${playerId}: ${finalFrameScores[numPlayerId] - score} + ${score} = ${finalFrameScores[numPlayerId]}`);
                        }
                    }
                } else {
                    console.log('当前回合分数变化已经记录到了turns中，不需要重复添加');
                }
            }

            console.log('计算的最终分数:', finalFrameScores);

            console.log('手动计算的最终分数:', finalFrameScores);

            // 输出调试日志
            console.log('局结束时的当前局分数:', finalFrameScores);
            console.log('当前回合的分数变化:', result.scores);
        }

        // 记录最终得分日志
        let frameScoresLog = `第 ${matchState.currentFrame.frameNumber} 局最终得分: `;
        for (const playerId in finalFrameScores) {
            if (Object.prototype.hasOwnProperty.call(finalFrameScores, playerId)) {
                frameScoresLog += `${getPlayerName(parseInt(playerId, 10))}: ${finalFrameScores[playerId]}分; `;
            }
        }
        addLogEntry(frameScoresLog.trim());

        // 输出详细的调试日志
        console.log('局结束时的最终得分计算:');
        console.log('当前局累计分数:', matchState.currentFrame.scores);
        console.log('当前回合分数变化:', result.scores);
        console.log('最终计算得分:', finalFrameScores);

        // Update matchState for the ended frame
        // 输出详细的调试日志，用于排查问题
        console.log('局结束时的最终得分计算详情:');
        console.log('当前局累计分数:', matchState.currentFrame.scores);
        console.log('最终计算得分:', finalFrameScores);

        const endedFrame: Frame = {
            ...matchState.currentFrame,
            scores: finalFrameScores,
            winner: winnerId ?? undefined // Assign winner if determined
        };
        const updatedFrames = [...matchState.frames, endedFrame];

        // 输出详细的调试日志，用于排查问题
        console.log('计算总分前的所有局:', updatedFrames.map(f => f.scores));

        const newTotalScores = calculateTotalScores(updatedFrames, playerConfigs); // Need this helper

        // 输出详细的调试日志，用于排查问题
        console.log('计算后的总分:', newTotalScores);

        // 设置局结束对话框中显示的特殊事件
        // 如果是普通黑5，不在局结束对话框中显示
        if (currentSpecialEvent === '普通黑5') {
          setFrameEndSpecialEvent(undefined);
        } else {
          setFrameEndSpecialEvent(currentSpecialEvent);
        }

        // 注意：我们在局结束时不重置特殊事件状态，因为我们需要在下一局开始时才重置
        // 这样可以确保特殊事件显示在触发特殊事件的选手卡片上，直到本局结束

        setMatchState(prev => ({
            ...prev!,
            frames: updatedFrames,
            totalScores: newTotalScores,
            // 不再在这里设置 remainingBalls 为空数组，这部分初始化将在 handleCloseFrameEndDialog 中进行
            // 这里保留当前状态，直到下一局真正开始
            remainingBalls: prev!.remainingBalls,
             // Prepare for the next frame (or end match)
             currentFrame: { // Reset for potential next frame
                 frameNumber: prev!.currentFrame.frameNumber + 1,
                 turns: [],
                 scores: Object.fromEntries(playerConfigs.map(p => [p.playerId, 0])), // Reset scores
             },
             currentTurn: { // Reset turn
                 playerId: -1, // Indicate no active turn until next frame starts
                 isBreakShot: true,
                 shots: [],
             }
        }));

        // 确定下一局玩家顺序
        let nextFramePlayerId: number;    // 开球玩家ID
        let nextFrameNextPlayerId: number; // 第二个击球的玩家ID
        let nextFramePreviousPlayerId: number; // 第三个击球的玩家ID

        // 根据 PRD 文档定义的规则确定下一局的击球顺序

        // 1. 大金、黄金9、黄金黑9、黄金开球或黑金开球情况下，保持原有的击球顺序
        if (result.specialEvent === '大金' ||
            result.specialEvent === '黄金9' ||
            result.specialEvent === '黄金黑9' ||
            result.specialEvent === '黄金开球' ||
            result.specialEvent === '黑金开球') {
          // 获取当前局的选手顺序
          const currentPlayerId = currentGameState.currentPlayerId;
          const nextPlayerId = currentGameState.nextPlayerId;
          const previousPlayerId = currentGameState.previousPlayerId;

          // 保持原有顺序
          nextFramePlayerId = currentPlayerId;
          nextFrameNextPlayerId = nextPlayerId;
          nextFramePreviousPlayerId = previousPlayerId;

          addLogEntry(`${result.specialEvent}后保持原有击球顺序，选手 ${getPlayerName(nextFramePlayerId)} 将开始下一局，选手 ${getPlayerName(nextFrameNextPlayerId)} 排第二个击球，选手 ${getPlayerName(nextFramePreviousPlayerId)} 排第三个击球`);
        }
        // 2. 正常获胜：当前选手在合法击球中将 9 号球打进袋并未犯规，其前序选手为败者
        else if ((result.specialEvent === '赢得本局' || balls && balls.includes(9)) && !hasFoul) {
          // 胜者是当前选手
          const winnerId = currentGameState.currentPlayerId;
          // 败者是前序选手
          const loserId = currentGameState.previousPlayerId;
          // 第三个选手
          const thirdPlayerId = currentGameState.nextPlayerId;

          // 胜者开球
          nextFramePlayerId = winnerId;
          // 败者第二个击球
          nextFrameNextPlayerId = loserId;
          // 第三个选手保持不变
          nextFramePreviousPlayerId = thirdPlayerId;

          addLogEntry(`胜者 ${getPlayerName(winnerId)} 将开始下一局，败者 ${getPlayerName(loserId)} 排第二个击球`);
        }
        // 3. 犯规失败：当前选手在将 9 号球打进袋并犯规，其前序选手获胜，当前选手为败者
        else if ((result.specialEvent === '赢得本局' || balls && balls.includes(9)) && hasFoul) {
          // 胜者是前序选手
          const winnerId = currentGameState.previousPlayerId;
          // 败者是当前选手
          const loserId = currentGameState.currentPlayerId;
          // 第三个选手
          const thirdPlayerId = currentGameState.nextPlayerId;

          // 胜者开球
          nextFramePlayerId = winnerId;
          // 败者第二个击球
          nextFrameNextPlayerId = loserId;
          // 第三个选手保持不变
          nextFramePreviousPlayerId = thirdPlayerId;

          addLogEntry(`胜者 ${getPlayerName(winnerId)} 将开始下一局，败者 ${getPlayerName(loserId)} 排第二个击球`);
        }
        // 4. 小金情况下，当前选手获胜，前序选手为败者
        else if (result.specialEvent === '小金') {
          // 胜者是当前选手
          const winnerId = currentGameState.currentPlayerId;
          // 败者是前序选手
          const loserId = currentGameState.previousPlayerId;
          // 第三个选手
          const thirdPlayerId = currentGameState.nextPlayerId;

          // 胜者开球
          nextFramePlayerId = winnerId;
          // 败者第二个击球
          nextFrameNextPlayerId = loserId;
          // 第三个选手保持不变
          nextFramePreviousPlayerId = thirdPlayerId;

          addLogEntry(`小金后，胜者 ${getPlayerName(winnerId)} 将开始下一局，败者 ${getPlayerName(loserId)} 排第二个击球`);
        }
        // 5. 黑小金情况下，前序选手获胜，当前选手为败者
        else if (result.specialEvent === '黑小金') {
          // 胜者是前序选手
          const winnerId = currentGameState.previousPlayerId;
          // 败者是当前选手
          const loserId = currentGameState.currentPlayerId;
          // 第三个选手
          const thirdPlayerId = currentGameState.nextPlayerId;

          // 胜者开球
          nextFramePlayerId = winnerId;
          // 败者第二个击球
          nextFrameNextPlayerId = loserId;
          // 第三个选手保持不变
          nextFramePreviousPlayerId = thirdPlayerId;

          addLogEntry(`黑小金后，胜者 ${getPlayerName(winnerId)} 将开始下一局，败者 ${getPlayerName(loserId)} 排第二个击球`);
        }
        // 6. 其他情况，保持原有击球顺序
        else {
          // 保持原有击球顺序
          nextFramePlayerId = currentGameState.currentPlayerId;
          nextFrameNextPlayerId = currentGameState.nextPlayerId;
          nextFramePreviousPlayerId = currentGameState.previousPlayerId;

          addLogEntry(`保持原有击球顺序，选手 ${getPlayerName(nextFramePlayerId)} 将开始下一局，选手 ${getPlayerName(nextFrameNextPlayerId)} 排第二个击球`);
        }

        // Create initial state for the next frame
        const nextFrameInitialState: GameState = {
          // Reset based on initial state logic or RuleEngine defaults
          remainingBalls: Array.from({ length: basicInfo.totalBalls }, (_, i) => i + 1),
          currentMinBall: 1,
          currentPlayerId: nextFramePlayerId, // 开球选手
          nextPlayerId: nextFrameNextPlayerId, // 第二个击球的选手
          // 使用前面计算好的 nextFramePreviousPlayerId
          previousPlayerId: nextFramePreviousPlayerId,
          isBreakShot: true,
          isObstructed: false,
          canLetShot: false,
          foulPool: 0,
          playerCount: playerConfigs.length
        };

        // Temporarily store the state for the next frame instead of setting it directly
        setPendingNextFrameState(nextFrameInitialState);

        // 添加局结束日志，包含特殊事件信息
        let endFrameLog = `第 ${endedFrame.frameNumber} 局结束。`;
        if (result.specialEvent) {
            endFrameLog += `特殊事件: ${result.specialEvent}. `;
        }
        if (winnerId) {
            endFrameLog += `胜者: ${getPlayerName(winnerId)}`;
        } else {
            endFrameLog += '无明确胜者';
        }
        // 强制添加局结束日志，确保即使有重复也会显示
        setLogEntries(prev => {
            const timestamp = new Date().toLocaleTimeString('zh-CN', { hour12: false });
            const fullMessage = `[${timestamp}] ${endFrameLog}`;
            return [...prev, fullMessage];
        });

        // 在弹出局结束对话框前，先等待 matchState 更新完成
        // 使用 setTimeout 确保在状态更新后再显示对话框
        setTimeout(() => {
          setFrameEndDialogOpen(true); // Show frame end dialog
        }, 100);
      } else if (continueTurn) {
        // 继续当前回合
        setMatchState(prev => ({
          ...prev!,
          remainingBalls: nextState.remainingBalls, // 更新剩余球
          currentTurn: { // 添加击球记录到当前回合
            ...prev!.currentTurn,
            shots: [
              ...prev!.currentTurn.shots,
              {
                result: 'pot',
                pottedBalls: balls.map(ballId => {
                  // 获取当前选手的球配置
                  const playerConfig = playerConfigs.find(p => p.playerId === currentGameState.currentPlayerId);
                  // 获取球的分值
                  const ballConfig = playerConfig?.balls.find(b => b.id === ballId);
                  return {
                    id: ballId,
                    points: ballConfig?.points || 0
                  };
                }),
                foulPoints: hasFoul ? getFoulPoints() : undefined
              }
            ]
          }
        }));
      } else {
        // 切换到下一个选手
        const previousPlayerId = currentGameState.currentPlayerId;
        const currentPlayerId = nextState.currentPlayerId;

        setMatchState(prev => {
          if (!prev) return null;

          // 结束当前回合
          const endedTurn: Turn = {
            ...prev.currentTurn,
            shots: [...prev.currentTurn.shots, {
              result: hasFoul ? 'foul' : 'pot',
              pottedBalls: balls.map(ballId => {
                // 获取当前选手的球配置
                const playerConfig = playerConfigs.find(p => p.playerId === currentGameState.currentPlayerId);
                // 获取球的分值
                const ballConfig = playerConfig?.balls.find(b => b.id === ballId);
                return {
                  id: ballId,
                  points: ballConfig?.points || 0
                };
              }),
              foulPoints: hasFoul ? getFoulPoints() : undefined
            }]
          };

          // 更新当前局的分数
          const updatedFrameScores = { ...prev.currentFrame.scores };
          for (const [playerId, score] of Object.entries(result.scores)) {
            const id = Number(playerId);
            updatedFrameScores[id] = (updatedFrameScores[id] || 0) + score;
          }

          // 更新当前局
          const updatedFrame: Frame = {
            ...prev.currentFrame,
            turns: [...prev.currentFrame.turns, endedTurn],
            scores: updatedFrameScores
          };

          return {
            ...prev,
            currentFrame: updatedFrame,
            remainingBalls: nextState.remainingBalls,
            currentTurn: { // 开始新回合
              playerId: currentPlayerId,
              isBreakShot: false,
              shots: []
            },
            players: prev.players.map(p => ({ // 更新选手角色
              ...p,
              role: p.id === currentPlayerId ? 'current' as const :
                    p.id === nextState.nextPlayerId ? 'next' as const :
                    p.id === previousPlayerId ? 'previous' as const : p.role
            }))
          };
        });

        // 记录选手切换
        addLogEntry(`回合结束。轮到选手 ${getPlayerName(currentPlayerId)} (${currentPlayerId}) 击球。`);
      }

      // 重置状态
      setSelectedBalls([]);
      setIsFoulChecked(false);
    } catch (error) {
      console.error("处理进球操作时出错:", error);
      setErrorMessage("处理进球操作时发生错误: " + (error instanceof Error ? error.message : String(error)));
    }
  }, [ruleEngine, currentGameState, matchState, getFoulPoints, playerConfigs, getPlayerName, addLogEntry, updateScores, logScoreChanges, basicInfo.totalBalls, calculateTotalScores, currentSpecialEvent, ruleConfig?.foulRules?.foulPenaltyPoints, ruleConfig?.foulRules?.foulPointsHandler, isSpecialEventTrigger]);

  // 直接处理未进球操作
  const handleDirectMiss = useCallback((hasFoul: boolean) => {
    if (!ruleEngine || !currentGameState || !matchState) {
      console.error("引擎或状态未初始化，无法处理击球。");
      setErrorMessage("发生内部错误，无法处理击球。请刷新或返回重试。");
      return;
    }

    // 使用规则配置中的犯规分值，不再需要用户选择

    try {
      // 记录未进球信息
      addLogEntry(`选手 ${getPlayerName(currentGameState.currentPlayerId)} 未进球${hasFoul ? '，同时犯规' : ''}。`);

      if (hasFoul) {
        // 如果有犯规，需要评估回合结果
        const result = ruleEngine.evaluateTurn(currentGameState, [], true);
        const nextState = result.nextState;

        // 更新特殊事件状态
        setCurrentSpecialEvent(result.specialEvent);

        // 当有特殊事件时，记录触发特殊事件的选手ID
        if (result.specialEvent && !isSpecialEventTrigger(currentGameState.currentPlayerId)) {
          setSpecialEventPlayerId(currentGameState.currentPlayerId);
          console.log(`识别到特殊事件: ${result.specialEvent}, 触发选手: ${currentGameState.currentPlayerId}`);
        }

        // 更新分数
        updateScores(result.scores);

        // 记录得分变化 - 只有当分数变化时才记录
        const hasScoreChanges = Object.values(result.scores).some(score => score !== 0);
        if (hasScoreChanges) {
          logScoreChanges(result.scores, matchState.currentFrame.scores, getPlayerName);
        }

        // 更新游戏状态
        setCurrentGameState(nextState);

        // 切换到下一个选手
        const previousPlayerId = currentGameState.currentPlayerId;
        const currentPlayerId = nextState.currentPlayerId;

        setMatchState(prev => {
          if (!prev) return null;

          // 结束当前回合
          const endedTurn: Turn = {
            ...prev.currentTurn,
            shots: [...prev.currentTurn.shots, {
              result: 'foul',
              pottedBalls: [],
              foulPoints: getFoulPoints()
            }]
          };

          // 更新当前局的分数
          const updatedFrameScores = { ...prev.currentFrame.scores };
          for (const [playerId, score] of Object.entries(result.scores)) {
            const id = Number(playerId);
            updatedFrameScores[id] = (updatedFrameScores[id] || 0) + score;
          }

          // 更新当前局
          const updatedFrame: Frame = {
            ...prev.currentFrame,
            turns: [...prev.currentFrame.turns, endedTurn],
            scores: updatedFrameScores
          };

          return {
            ...prev,
            currentFrame: updatedFrame,
            remainingBalls: nextState.remainingBalls,
            currentTurn: { // 开始新回合
              playerId: currentPlayerId,
              isBreakShot: false,
              shots: []
            },
            players: prev.players.map(p => ({ // 更新选手角色
              ...p,
              role: p.id === currentPlayerId ? 'current' as const :
                    p.id === nextState.nextPlayerId ? 'next' as const :
                    p.id === previousPlayerId ? 'previous' as const : p.role
            }))
          };
        });

        // 记录选手切换
        addLogEntry(`回合结束。轮到选手 ${getPlayerName(currentPlayerId)} (${currentPlayerId}) 击球。`);
      } else {
        // 如果没有犯规，评估回合结果
        const result = ruleEngine.evaluateTurn(currentGameState, [], false);
        const nextState = result.nextState;
        setCurrentGameState(nextState);

        // 切换到下一个选手
        const previousPlayerId = currentGameState.currentPlayerId;
        const currentPlayerId = nextState.currentPlayerId;

        setMatchState(prev => {
          if (!prev) return null;

          // 结束当前回合
          const endedTurn: Turn = {
            ...prev.currentTurn,
            shots: [...prev.currentTurn.shots, {
              result: 'miss',
              pottedBalls: []
            }]
          };

          // 更新当前局
          const updatedFrame: Frame = {
            ...prev.currentFrame,
            turns: [...prev.currentFrame.turns, endedTurn]
          };

          return {
            ...prev,
            currentFrame: updatedFrame,
            remainingBalls: nextState.remainingBalls,
            currentTurn: { // 开始新回合
              playerId: currentPlayerId,
              isBreakShot: false,
              shots: []
            },
            players: prev.players.map(p => ({ // 更新选手角色
              ...p,
              role: p.id === currentPlayerId ? 'current' as const :
                    p.id === nextState.nextPlayerId ? 'next' as const :
                    p.id === previousPlayerId ? 'previous' as const : p.role
            }))
          };
        });

        // 记录选手切换
        addLogEntry(`回合结束。轮到选手 ${getPlayerName(currentPlayerId)} (${currentPlayerId}) 击球。`);
      }

      // 重置状态
      setSelectedBalls([]);
      setIsFoulChecked(false);
    } catch (error) {
      console.error("处理未进球操作时出错:", error);
      setErrorMessage("处理未进球操作时发生错误: " + (error instanceof Error ? error.message : String(error)));
    }
  }, [ruleEngine, currentGameState, matchState, getFoulPoints, getPlayerName, addLogEntry, updateScores, logScoreChanges, isSpecialEventTrigger]);

  // 直接处理让球操作
  const handleDirectConcede = useCallback(() => {
    if (!ruleEngine || !currentGameState || !matchState) {
      console.error("引擎或状态未初始化，无法处理击球。");
      setErrorMessage("发生内部错误，无法处理击球。请刷新或返回重试。");
      return;
    }

    try {
      // 评估回合结果
      const result = ruleEngine.evaluateTurn(currentGameState, [], false);
      const nextState = result.nextState;
      setCurrentGameState(nextState);

      // 记录让球信息
      addLogEntry(`选手 ${getPlayerName(currentGameState.currentPlayerId)} 让球给选手 ${getPlayerName(nextState.currentPlayerId)}。`);

      // 切换到下一个选手
      const previousPlayerId = currentGameState.currentPlayerId;
      const currentPlayerId = nextState.currentPlayerId;

      setMatchState(prev => {
        if (!prev) return null;

        // 结束当前回合
        const endedTurn: Turn = {
          ...prev.currentTurn,
          shots: [...prev.currentTurn.shots, {
            result: 'concede'
          }]
        };

        // 更新当前局
        const updatedFrame: Frame = {
          ...prev.currentFrame,
          turns: [...prev.currentFrame.turns, endedTurn]
        };

        return {
          ...prev,
          currentFrame: updatedFrame,
          remainingBalls: nextState.remainingBalls,
          currentTurn: { // 开始新回合
            playerId: currentPlayerId,
            isBreakShot: false,
            shots: []
          },
          players: prev.players.map(p => ({ // 更新选手角色
            ...p,
            role: p.id === currentPlayerId ? 'current' as const :
                  p.id === nextState.nextPlayerId ? 'next' as const :
                  p.id === previousPlayerId ? 'previous' as const : p.role
          }))
        };
      });

      // 记录选手切换
      addLogEntry(`回合结束。轮到选手 ${getPlayerName(currentPlayerId)} (${currentPlayerId}) 击球。`);

      // 重置状态
      setSelectedBalls([]);
      setIsFoulChecked(false);
    } catch (error) {
      console.error("处理让球操作时出错:", error);
      setErrorMessage("处理让球操作时发生错误: " + (error instanceof Error ? error.message : String(error)));
    }
  }, [ruleEngine, currentGameState, matchState, getPlayerName, addLogEntry]);

  // 处理局结束逻辑已经移到handleDirectPot函数中直接处理

  // 处理让球
  const handleLetShot = useCallback(() => {
    if (!ruleEngine || !currentGameState) return;

    try {
      // 调用规则引擎的让球处理
      const nextState = ruleEngine.handleLetShot(currentGameState);

      // 更新游戏状态
      setCurrentGameState(nextState);

      // 更新 UI 状态
      setMatchState(prev => {
        if (!prev) return null;

        // 更新选手角色
        const updatedPlayers = prev.players.map(player => ({
          ...player,
          role: player.id === nextState.currentPlayerId
                 ? 'current' as PlayerRole
                 : player.id === nextState.nextPlayerId
                   ? 'next' as PlayerRole
                   : 'previous' as PlayerRole
        }));

        // 创建新的回合记录
        const newTurn: Turn = {
          playerId: nextState.currentPlayerId,
          isBreakShot: false,
          shots: []
        };

        // 在当前回合添加让球记录
        const updatedCurrentTurn: Turn = {
          ...prev.currentTurn,
          shots: [
            ...prev.currentTurn.shots,
            { result: 'concede' }
          ]
        };

        // 更新当前局
        const updatedFrame: Frame = {
          ...prev.currentFrame,
          turns: [...prev.currentFrame.turns, updatedCurrentTurn]
        };

        return {
          ...prev,
          currentFrame: updatedFrame,
          currentTurn: newTurn,
          players: updatedPlayers
        };
      });

      // 添加日志
      addLogEntry(`选手 ${getPlayerName(currentGameState.currentPlayerId)} 让球给选手 ${getPlayerName(nextState.currentPlayerId)}`);

    } catch (error) {
      console.error("处理让球时出错:", error);
      setErrorMessage("处理让球时发生错误: " + (error instanceof Error ? error.message : String(error)));
    }
  }, [ruleEngine, currentGameState, getPlayerName, addLogEntry]);

  const handleConfirmShot = useCallback(() => {
    if (!ruleEngine || !currentGameState || !matchState || !currentAction) {
      console.error("引擎或状态未初始化，无法处理击球。");
      setErrorMessage("发生内部错误，无法处理击球。请刷新或返回重试。");
      return;
    }

    let pocketedBalls: number[] = [];
    let hasFoul = false;

    switch (currentAction) {
      case 'pot':
        pocketedBalls = selectedBalls;
        hasFoul = false;
        // 记录进球信息
        addLogEntry(`选手 ${getPlayerName(currentGameState.currentPlayerId)} 进了 ${pocketedBalls.join(', ')} 号球。`);
        break;
      case 'miss':
        pocketedBalls = [];
        hasFoul = false;
        // 记录未进球信息
        addLogEntry(`选手 ${getPlayerName(currentGameState.currentPlayerId)} 未进球。`);

        // 未进球情况下直接切换选手，不计算分数变化
        try {
          // 评估回合结果
          const result = ruleEngine.evaluateTurn(currentGameState, [], false);
          const nextState = result.nextState;

          // 检查是否有特殊事件或局结束标志
          if (nextState.isFrameOver) {
            console.log('检测到局结束标志，处理局结束逻辑');
            // 如果局结束，调用 handleDirectPot 处理局结束逻辑
            handleDirectPot([], false);
            return; // 提前返回，不执行后面的代码
          }

          // 更新游戏状态
          setCurrentGameState(nextState);

          // 更新 UI 状态
          setMatchState(prev => {
            if (!prev) return null;

            // 更新选手角色
            const updatedPlayers = prev.players.map(player => ({
              ...player,
              role: player.id === nextState.currentPlayerId
                     ? 'current' as PlayerRole
                     : player.id === nextState.nextPlayerId
                       ? 'next' as PlayerRole
                       : 'previous' as PlayerRole
            }));

            // 结束当前回合
            const endedTurn: Turn = {
              ...prev.currentTurn,
              shots: [...prev.currentTurn.shots, {
                result: 'miss',
                pottedBalls: []
              }]
            };

            // 创建新的回合
            const newTurn: Turn = {
              playerId: nextState.currentPlayerId,
              isBreakShot: false,
              shots: []
            };

            // 更新当前局
            const updatedFrame: Frame = {
              ...prev.currentFrame,
              turns: [...prev.currentFrame.turns, endedTurn]
            };

            // 记录选手切换 - 已经在前面记录了未进球，这里只记录击球顺序
            // addLogEntry 函数已经内置了去重机制，不需要额外的去重逻辑
            addLogEntry(`轮到选手 ${getPlayerName(nextState.currentPlayerId)} (${nextState.currentPlayerId}) 击球。`);

            return {
              ...prev,
              currentFrame: updatedFrame,
              currentTurn: newTurn,
              players: updatedPlayers
            };
          });

          // 关闭对话框
          setShotDialogOpen(false);
          return; // 提前返回，不执行后面的代码
        } catch (error) {
          console.error("处理未进球时出错:", error);
          setErrorMessage("处理未进球时发生错误: " + (error instanceof Error ? error.message : String(error)));
        }
        break;
      case 'foul':
        pocketedBalls = selectedBalls;
        hasFoul = true;
        // 记录犯规信息
        addLogEntry(`选手 ${getPlayerName(currentGameState.currentPlayerId)} 犯规${pocketedBalls.length > 0 ? `，同时进了 ${pocketedBalls.join(', ')} 号球` : ''}。罚分: ${getFoulPoints()}。`);
        break;
      case 'concede':
        pocketedBalls = [];
        hasFoul = true;
        // 记录认输信息
        addLogEntry(`选手 ${getPlayerName(currentGameState.currentPlayerId)} (${currentGameState.currentPlayerId}) 认输。`);
        setFrameEndDialogOpen(true);
        setMatchEndDialogOpen(true);
        setShotDialogOpen(false);
        return;
    }

    try {
      // --- Evaluate Turn ---
      const result = ruleEngine.evaluateTurn(currentGameState, pocketedBalls, hasFoul); // Fixed: Removed 4th arg (foulPoints)
      const nextState = result.nextState;

      // 更新特殊事件状态
      setCurrentSpecialEvent(result.specialEvent);

      // 当有特殊事件时，记录触发特殊事件的选手ID和特殊事件日志
      if (result.specialEvent && !isSpecialEventTrigger(currentGameState.currentPlayerId)) {
        setSpecialEventPlayerId(currentGameState.currentPlayerId);
        console.log(`识别到特殊事件: ${result.specialEvent}, 触发选手: ${currentGameState.currentPlayerId}`);

        // 记录特殊事件日志
        let specialEventLog = `特殊事件: ${result.specialEvent}! `;
        if (result.specialEvent === '大金') {
          specialEventLog += `选手 ${getPlayerName(currentGameState.currentPlayerId)} 开球轮清台!`;
        } else if (result.specialEvent === '黑大金') {
          specialEventLog += `选手 ${getPlayerName(currentGameState.currentPlayerId)} 开球轮犯规清台!`;
        } else if (result.specialEvent === '小金') {
          specialEventLog += `选手 ${getPlayerName(currentGameState.currentPlayerId)} 清台小金球组!`;
        } else if (result.specialEvent === '黑小金') {
          specialEventLog += `选手 ${getPlayerName(currentGameState.currentPlayerId)} 犯规清台小金球组!`;
        } else if (result.specialEvent === '黄金9') {
          specialEventLog += `选手 ${getPlayerName(currentGameState.currentPlayerId)} 打进黄金9号球!`;
        } else if (result.specialEvent === '黑9') {
          specialEventLog += `选手 ${getPlayerName(currentGameState.currentPlayerId)} 犯规打进9号球!`;
        }
        addLogEntry(specialEventLog);
      }

      // --- Update Scores and Log ---
      // 更新分数
      updateScores(result.scores);

      // 记录得分变化 - 只有当分数变化时才记录
      const hasScoreChanges = Object.values(result.scores).some(score => score !== 0);
      if (hasScoreChanges) {
        logScoreChanges(result.scores, matchState.currentFrame.scores, getPlayerName);
      }

      // --- Determine Turn Continuation ---
      // Corrected logic: Continue only if no foul, potted balls, AND balls remaining
      const continueTurn = !result.hasFoul && result.pocketedBalls.length > 0 && nextState.remainingBalls.length > 0;

      // --- Update Game State (do this early to use nextState for checks) ---
      setCurrentGameState(nextState);

      // --- Check for Frame/Match End ---
      // 检查局结束
      const isFrameOver = nextState.remainingBalls.length === 0 ||
                         result.specialEvent === '大金' ||
                         result.specialEvent === '小金' ||
                         result.specialEvent === '黑小金' ||
                         result.specialEvent === '黄金9' ||
                         result.specialEvent === '黑9' ||
                         nextState.isFrameOver === true;

      // 计算每个选手赢的局数
      const framesWon: Record<number, number> = {};
      matchState.players.forEach(player => {
        framesWon[player.id] = matchState.frames
          .filter(frame => frame.winner === player.id)
          .length;
      });

      // 检查比赛是否结束
      const matchEndStatus = ruleEngine.checkMatchEnd(
        matchState.totalScores,
        matchState.frames.length + 1, // 包括当前局
        framesWon
      );

      const isMatchOver = matchEndStatus.isEnd;
      if (isMatchOver) {
        setMatchWinner(matchEndStatus.winnerId || null);
        if (matchEndStatus.reason) {
          addLogEntry(`比赛结束！${matchEndStatus.reason}`);
        }
        if (matchEndStatus.winnerId !== undefined) {
          addLogEntry(`获胜者: ${getPlayerName(matchEndStatus.winnerId)}`);
        } else {
          addLogEntry('比赛结果: 平局');
        }

        // 打开比赛结束对话框
        setMatchEndDialogOpen(true);
      }

      // 特殊事件处理
      if (result.specialEvent) {
        setCurrentSpecialEvent(result.specialEvent);
        // 为特殊事件增加特殊提示
        addLogEntry(`特殊事件: ${result.specialEvent}! 选手 ${getPlayerName(currentGameState.currentPlayerId)} ${result.specialEvent.includes('黑') ? '被罚分' : '获得奖励'}!`);
      }


      if (isMatchOver) {
        // --- Handle Match End ---
        addLogEntry(`比赛结束！`); // Placeholder log
        // setMatchWinner(endStatus.winner);
        // setMatchEndDialogOpen(true);
        // ... (full match end logic needed) ...

      } else if (isFrameOver) {
        // --- Handle Frame End ---
        const winnerId = (result.specialEvent === '大金' || result.specialEvent === '小金')
                           ? currentGameState.currentPlayerId // Winner is current player on special win
                           : (nextState.remainingBalls.length === 0 && !result.hasFoul)
                             ? currentGameState.currentPlayerId // Winner is current player on normal clear
                             : null; // Or determine winner differently based on rules (e.g., opponent wins on foul clear)

        setFrameWinner(winnerId);

         // 处理局结束时的分数
         // 对于所有情况，都需要将当前回合的分数变化添加到当前局的累计分数中
         let finalFrameScores: Record<number, number> = {};

         // 对于大金、黑大金、黄金开球、黑金开球等特殊事件，直接使用 result.scores 作为本局最终得分
         // 这是因为这些特殊事件会直接结束本局，不需要累加到之前的分数
         if (result.specialEvent === '大金' ||
             result.specialEvent === '黑大金' ||
             result.specialEvent === '黄金开球' ||
             result.specialEvent === '黑金开球') {
             // 直接使用 result.scores 作为本局最终得分
             for (const [playerId, score] of Object.entries(result.scores)) {
                 const id = Number(playerId);
                 finalFrameScores[id] = score;
             }

             // 确保所有选手都有分数记录
             for (const player of playerConfigs) {
                 if (finalFrameScores[player.playerId] === undefined) {
                     finalFrameScores[player.playerId] = 0;
                 }
             }

             // 特殊事件日志记录
             console.log(`特殊事件: ${result.specialEvent}, 分数: `, finalFrameScores);
         } else {
             // 对于普通情况，将当前回合的分数变化添加到当前局的累计分数中
             finalFrameScores = { ...matchState.currentFrame.scores };
             for (const [playerId, score] of Object.entries(result.scores)) {
                 const id = Number(playerId);
                 finalFrameScores[id] = (finalFrameScores[id] || 0) + score;
             }
         }

         // 记录最终得分日志
         let frameScoresLog = `第 ${matchState.currentFrame.frameNumber} 局最终得分: `;
         for (const playerId in finalFrameScores) {
             if (Object.prototype.hasOwnProperty.call(finalFrameScores, playerId)) {
                 frameScoresLog += `${getPlayerName(parseInt(playerId, 10))}: ${finalFrameScores[playerId]}分; `;
             }
         }
         addLogEntry(frameScoresLog.trim());

         // Update matchState for the ended frame
         const endedFrame: Frame = {
             ...matchState.currentFrame,
             scores: finalFrameScores,
             winner: winnerId ?? undefined // Assign winner if determined
         };
        const updatedFrames = [...matchState.frames, endedFrame];
        const newTotalScores = calculateTotalScores(updatedFrames, playerConfigs); // Need this helper


        // 重置特殊事件状态
        // 注意：我们在局结束时不重置特殊事件状态，因为我们需要在下一局开始时才重置
        // 这样可以确保特殊事件显示在触发特殊事件的选手卡片上，直到本局结束

        setMatchState(prev => ({
            ...prev!,
            frames: updatedFrames,
            totalScores: newTotalScores,
            // 不再在这里设置 remainingBalls 为空数组，这部分初始化将在 handleCloseFrameEndDialog 中进行
            // 这里保留当前状态，直到下一局真正开始
            remainingBalls: prev!.remainingBalls,
             // Prepare for the next frame (or end match)
             currentFrame: { // Reset for potential next frame
                 frameNumber: prev!.currentFrame.frameNumber + 1,
                 turns: [],
                 scores: Object.fromEntries(playerConfigs.map(p => [p.playerId, 0])), // Reset scores
             },
             currentTurn: { // Reset turn
                 playerId: -1, // Indicate no active turn until next frame starts
                 isBreakShot: true,
                 shots: [],
             }
        }));

        // Reset GameState for the next frame - Simplified logic
        // 已经在前面记录了最终得分，这里不再重复记录

        // 确定下一局玩家顺序
        let nextFramePlayerId: number;    // 开球玩家ID
        let nextFrameNextPlayerId: number; // 第二个击球的玩家ID
        let nextFramePreviousPlayerId: number; // 第三个击球的玩家ID

        // 根据 PRD 文档定义的规则确定下一局的击球顺序

        // 1. 大金、黄金9、黄金开球或黑金开球情况下，保持原有的击球顺序
        if (result.specialEvent === '大金' || result.specialEvent === '黄金9' ||
            result.specialEvent === '黄金开球' || result.specialEvent === '黑金开球' ||
            currentSpecialEvent === '黄金黑9') {
          // 获取当前局的选手顺序
          const currentPlayerId = currentGameState.currentPlayerId;
          const nextPlayerId = currentGameState.nextPlayerId;
          const previousPlayerId = currentGameState.previousPlayerId;

          // 保持原有顺序
          nextFramePlayerId = currentPlayerId;
          nextFrameNextPlayerId = nextPlayerId;
          nextFramePreviousPlayerId = previousPlayerId;

          addLogEntry(`${result.specialEvent}后保持原有击球顺序，选手 ${getPlayerName(nextFramePlayerId)} 将开始下一局，选手 ${getPlayerName(nextFrameNextPlayerId)} 排第二个击球，选手 ${getPlayerName(nextFramePreviousPlayerId)} 排第三个击球`);
        }
        // 2. 正常获胜：当前选手在合法击球中将 9 号球打进袋并未犯规，其前序选手为败者
        else if (result.specialEvent === '赢得本局' && !result.hasFoul && pocketedBalls.includes(9)) {
          // 胜者是当前选手
          const winnerId = currentGameState.currentPlayerId;
          // 败者是前序选手
          const loserId = currentGameState.previousPlayerId;
          // 第三个选手
          const thirdPlayerId = currentGameState.nextPlayerId;

          // 胜者开球
          nextFramePlayerId = winnerId;
          // 败者第二个击球
          nextFrameNextPlayerId = loserId;
          // 第三个选手保持不变
          nextFramePreviousPlayerId = thirdPlayerId;

          addLogEntry(`胜者 ${getPlayerName(winnerId)} 将开始下一局，败者 ${getPlayerName(loserId)} 排第二个击球`);
        }
        // 3. 犯规失败：当前选手在将 9 号球打进袋并犯规，其前序选手获胜，当前选手为败者
        else if (result.specialEvent === '赢得本局' && result.hasFoul && pocketedBalls.includes(9)) {
          // 胜者是前序选手
          const winnerId = currentGameState.previousPlayerId;
          // 败者是当前选手
          const loserId = currentGameState.currentPlayerId;
          // 第三个选手
          const thirdPlayerId = currentGameState.nextPlayerId;

          // 胜者开球
          nextFramePlayerId = winnerId;
          // 败者第二个击球
          nextFrameNextPlayerId = loserId;
          // 第三个选手保持不变
          nextFramePreviousPlayerId = thirdPlayerId;

          addLogEntry(`胜者 ${getPlayerName(winnerId)} 将开始下一局，败者 ${getPlayerName(loserId)} 排第二个击球`);
        }
        // 4. 小金情况下，当前选手获胜，前序选手为败者
        else if (result.specialEvent === '小金') {
          // 胜者是当前选手
          const winnerId = currentGameState.currentPlayerId;
          // 败者是前序选手
          const loserId = currentGameState.previousPlayerId;
          // 第三个选手
          const thirdPlayerId = currentGameState.nextPlayerId;

          // 胜者开球
          nextFramePlayerId = winnerId;
          // 败者第二个击球
          nextFrameNextPlayerId = loserId;
          // 第三个选手保持不变
          nextFramePreviousPlayerId = thirdPlayerId;

          addLogEntry(`小金后，胜者 ${getPlayerName(winnerId)} 将开始下一局，败者 ${getPlayerName(loserId)} 排第二个击球`);
        }
        // 5. 黑小金情况下，前序选手获胜，当前选手为败者
        else if (result.specialEvent === '黑小金') {
          // 胜者是前序选手
          const winnerId = currentGameState.previousPlayerId;
          // 败者是当前选手
          const loserId = currentGameState.currentPlayerId;
          // 第三个选手
          const thirdPlayerId = currentGameState.nextPlayerId;

          // 胜者开球
          nextFramePlayerId = winnerId;
          // 败者第二个击球
          nextFrameNextPlayerId = loserId;
          // 第三个选手保持不变
          nextFramePreviousPlayerId = thirdPlayerId;

          addLogEntry(`黑小金后，胜者 ${getPlayerName(winnerId)} 将开始下一局，败者 ${getPlayerName(loserId)} 排第二个击球`);
        }
        // 6. 其他情况，保持原有击球顺序
        else {
          // 保持原有击球顺序
          nextFramePlayerId = currentGameState.currentPlayerId;
          nextFrameNextPlayerId = currentGameState.nextPlayerId;
          nextFramePreviousPlayerId = currentGameState.previousPlayerId;

          addLogEntry(`保持原有击球顺序，选手 ${getPlayerName(nextFramePlayerId)} 将开始下一局，选手 ${getPlayerName(nextFrameNextPlayerId)} 排第二个击球`);
        }

        // Create initial state for the next frame
        const nextFrameInitialState: GameState = {
          // Reset based on initial state logic or RuleEngine defaults
          remainingBalls: Array.from({ length: basicInfo.totalBalls }, (_, i) => i + 1),
          currentMinBall: 1,
          currentPlayerId: nextFramePlayerId, // 开球选手
          nextPlayerId: nextFrameNextPlayerId, // 第二个击球的选手
          // 使用前面计算好的 nextFramePreviousPlayerId
          previousPlayerId: nextFramePreviousPlayerId,
          isBreakShot: true,
          isObstructed: false,
          canLetShot: false,
          foulPool: 0,
          playerCount: playerConfigs.length
        };

        // Temporarily store the state for the next frame instead of setting it directly
        setPendingNextFrameState(nextFrameInitialState);

        // 添加局结束日志，包含特殊事件信息
        let endFrameLog = `第 ${endedFrame.frameNumber} 局结束。`;
        if (currentSpecialEvent) {
            // 使用当前特殊事件状态，而不是规则引擎返回的特殊事件
            endFrameLog += `特殊事件: ${currentSpecialEvent}. `;
        } else if (result.specialEvent) {
            endFrameLog += `特殊事件: ${result.specialEvent}. `;
        }
        if (winnerId) {
            endFrameLog += `胜者: ${getPlayerName(winnerId)}`;
        } else {
            endFrameLog += '无明确胜者';
        }
        // 强制添加局结束日志，确保即使有重复也会显示
        setLogEntries(prev => {
            const timestamp = new Date().toLocaleTimeString('zh-CN', { hour12: false });
            const fullMessage = `[${timestamp}] ${endFrameLog}`;
            return [...prev, fullMessage];
        });
        // 在弹出局结束对话框前，先等待 matchState 更新完成
        // 使用 setTimeout 确保在状态更新后再显示对话框
        setTimeout(() => {
          setFrameEndDialogOpen(true); // Show frame end dialog
        }, 100);

    } else if (continueTurn) {
        // --- Continue Turn ---
        setMatchState(prev => ({
            ...prev!,
            remainingBalls: nextState.remainingBalls, // Update remaining balls
            currentTurn: { // Add shot to the current turn
                 ...prev!.currentTurn,
                 shots: [
                     ...prev!.currentTurn.shots,
                     {
                         result: currentAction!,
                         // Fixed: Map selectedBalls (number[]) to PottedBall[]
                         pottedBalls: selectedBalls.map(ballId => {
                           // 获取当前选手的球配置
                           const playerConfig = playerConfigs.find(p => p.playerId === currentGameState.currentPlayerId);
                           // 获取球的分值
                           const ballConfig = playerConfig?.balls.find(b => b.id === ballId);
                           return {
                             id: ballId,
                             points: ballConfig?.points || 0
                           };
                         }),
                         foulPoints: hasFoul ? getFoulPoints() : undefined
                     }
                 ]
            }
            // Player roles remain the same
        }));
        // Log score changes for the shot - 只有当分数变化时才记录
        const hasScoreChanges = Object.values(result.scores).some(score => score !== 0);
        if (hasScoreChanges) {
          logScoreChanges(result.scores, matchState.currentFrame.scores, getPlayerName);
        }
        // 已经在前面记录了击球顺序，这里不再重复记录

    } else {
        // --- Switch Player ---
        const previousPlayerId = currentGameState.currentPlayerId;
        const currentPlayerId = nextState.currentPlayerId; // RuleEngine determined the next player

        setMatchState(prev => {
            if (!prev) return null; // Guard against null state

            const endedTurn: Turn = { // Finalize the ended turn
                 ...prev.currentTurn,
                 shots: [...prev.currentTurn.shots, {
                     result: currentAction!,
                     // Fixed: Map selectedBalls (number[]) to PottedBall[]
                     pottedBalls: selectedBalls.map(ballId => {
                       // 获取当前选手的球配置
                       const playerConfig = playerConfigs.find(p => p.playerId === currentGameState.currentPlayerId);
                       // 获取球的分值
                       const ballConfig = playerConfig?.balls.find(b => b.id === ballId);
                       return {
                         id: ballId,
                         points: ballConfig?.points || 0
                       };
                     }),
                     foulPoints: hasFoul ? getFoulPoints() : undefined
                 }]
            };
            // 更新当前局的分数，将新的分数变化添加到当前局的累计分数中
            const updatedFrameScores = { ...prev.currentFrame.scores };

            // 合并新分数
            for (const [playerId, score] of Object.entries(result.scores)) {
                const id = Number(playerId);
                updatedFrameScores[id] = (updatedFrameScores[id] || 0) + score;
            }

            const updatedFrame: Frame = { // Add ended turn to current frame
                 ...prev.currentFrame,
                 turns: [...prev.currentFrame.turns, endedTurn],
                 scores: updatedFrameScores // 使用累计分数
            };

            return {
                ...prev,
                currentFrame: updatedFrame,
                remainingBalls: nextState.remainingBalls, // Update remaining balls
                currentTurn: { // Start a new turn for the next player
                    playerId: currentPlayerId,
                    isBreakShot: false, // Usually not a break shot
                    shots: []
                },
                players: prev.players.map(p => ({ // Update player roles
                     ...p,
                     role: p.id === currentPlayerId ? 'current' as const :
                           p.id === nextState.nextPlayerId ? 'next' as const : // Use nextState's nextPlayerId
                           p.id === previousPlayerId ? 'previous' as const : p.role // Mark previous player
                }))
            };
        });
         // 记录分数变化
         // 注意：这里不再调用 logScoreChanges，因为已经在前面调用过了
         // 避免重复记录分数变化
        // 合并两条日志，避免重复
        // addLogEntry 函数已经内置了去重机制，不需要额外的去重逻辑
        addLogEntry(`回合结束。轮到选手 ${getPlayerName(currentPlayerId)} (${currentPlayerId}) 击球。`);
    }

  } catch (error) {
    console.error("处理击球或调用规则引擎时出错:", error);
    setErrorMessage("处理击球时发生错误: " + (error instanceof Error ? error.message : String(error)));
  }

  setShotDialogOpen(false);
  setSelectedBalls([]);
  setCurrentAction(null);
 }, [ruleEngine, currentGameState, matchState, currentAction, selectedBalls, getFoulPoints, playerConfigs, basicInfo.totalBalls, getPlayerName, addLogEntry, calculateTotalScores, logScoreChanges, updateScores, handleDirectPot, currentSpecialEvent, isSpecialEventTrigger]);

  // Close the frame end dialog and potentially start the next frame
  const handleCloseFrameEndDialog = () => {
    setFrameEndDialogOpen(false);
    // 清除特殊事件和触发特殊事件的选手ID，准备下一局
    setCurrentSpecialEvent(undefined);
    setFrameEndSpecialEvent(undefined);
    setSpecialEventPlayerId(null);
    if (pendingNextFrameState) {
      // Apply the pending state for the next frame
      setCurrentGameState(pendingNextFrameState);

      // 同步更新 matchState 中的关键属性，使其与 currentGameState 保持一致
      setMatchState(prev => {
        if (!prev) return prev;

        // 使用 pendingNextFrameState 初始化一个新的 Turn
        const nextTurn: Turn = {
          playerId: pendingNextFrameState.currentPlayerId,
          isBreakShot: pendingNextFrameState.isBreakShot,
          shots: []
        };

        // 更新 players 的 role，以便正确显示当前选手
        const updatedPlayers = prev.players.map(player => ({
          ...player,
          role: player.id === pendingNextFrameState.currentPlayerId
                 ? 'current' as PlayerRole
                 : player.id === pendingNextFrameState.nextPlayerId
                   ? 'next' as PlayerRole
                   : 'previous' as PlayerRole
        }));

        // 初始化下一局的分数记录
        const nextFrameScores: Record<number, number> = {};
        prev.players.forEach(player => {
          nextFrameScores[player.id] = 0;
        });

        return {
          ...prev,
          remainingBalls: [...pendingNextFrameState.remainingBalls], // 更新剩余球
          currentTurn: nextTurn, // 更新当前回合
          players: updatedPlayers, // 更新选手角色
          currentFrame: { // 更新当前局信息
            frameNumber: prev.frames.length + 1, // 正确设置局数为已完成局数+1
            turns: [], // 清空回合记录
            scores: nextFrameScores // 初始化分数
          }
        };
      });

      // Log start of the next frame
      const nextFrameNumber = (matchState?.frames.length ?? 0) + 1; // Calculate based on the updated frames count
      // 添加下一局开始的日志，包含更详细的击球顺序信息
      let nextFrameLog = `第 ${nextFrameNumber} 局开始。开球轮，轮到选手 ${getPlayerName(pendingNextFrameState.currentPlayerId)} (${pendingNextFrameState.currentPlayerId}) 击球。`;
      // 添加击球顺序信息
      nextFrameLog += ` 击球顺序: 1. ${getPlayerName(pendingNextFrameState.currentPlayerId)}, 2. ${getPlayerName(pendingNextFrameState.nextPlayerId)}, 3. ${getPlayerName(pendingNextFrameState.previousPlayerId)}`;
      // 强制添加下一局开始日志，确保即使有重复也会显示
      setLogEntries(prev => {
          const timestamp = new Date().toLocaleTimeString('zh-CN', { hour12: false });
          const fullMessage = `[${timestamp}] ${nextFrameLog}`;
          return [...prev, fullMessage];
      });

      // Clear the pending state
      setPendingNextFrameState(null);
    }
  };

  // 处理返回首页
  const handleReturnHome = () => {
    navigate('/');
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4 }}>
      {/* 标题栏 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4">
          {basicInfo.name}
        </Typography>
        <Button
          variant="outlined"
          startIcon={<HomeIcon />}
          onClick={handleReturnHome}
        >
          返回首页
        </Button>
      </Box>

      {/* 比赛信息 */}
      <Paper sx={{ p: 2, mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 1 }}>
          比赛信息
        </Typography>

        <Grid container spacing={2}>
          {/* 基本信息 */}
          <Grid item xs={12} md={4}>
            <Paper variant="outlined" sx={{ p: 1.5 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, borderBottom: '1px solid rgba(0,0,0,0.1)', pb: 0.5 }}>
                基本信息
              </Typography>
              <Typography variant="body2">
                比赛日期：{basicInfo.date}
              </Typography>
              <Typography variant="body2">
                比赛地点：{basicInfo.location}
              </Typography>
              <Typography variant="body2">
                比赛模式：{
                  basicInfo.mode === 'frame' ? '固定局数' :
                  basicInfo.mode === 'time' ? '限时' :
                  basicInfo.mode === 'score' ? '定分' : '手动'
                }
              </Typography>
              {basicInfo.mode === 'frame' && basicInfo.settings.frame && (
                <>
                  <Typography variant="body2">
                    总局数：{basicInfo.settings.frame.totalFrames}
                  </Typography>
                  <Typography variant="body2">
                    获胜局数：{basicInfo.settings.frame.winningFrames}
                  </Typography>
                </>
              )}
              {basicInfo.mode === 'time' && basicInfo.settings.time && (
                <Typography variant="body2">
                  比赛时长：{basicInfo.settings.time.duration} 分钟
                  {basicInfo.settings.time.extraTime > 0 &&
                    `（加时 ${basicInfo.settings.time.extraTime} 分钟）`}
                </Typography>
              )}
              {basicInfo.mode === 'score' && basicInfo.settings.score && (
                <Typography variant="body2">
                  目标分数：{basicInfo.settings.score.targetScore}
                  {basicInfo.settings.score.minFrames &&
                    `（最少 ${basicInfo.settings.score.minFrames} 局）`}
                </Typography>
              )}
            </Paper>
          </Grid>

          {/* 当前进度 */}
          <Grid item xs={12} md={4}>
            <Paper variant="outlined" sx={{ p: 1.5 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, borderBottom: '1px solid rgba(0,0,0,0.1)', pb: 0.5 }}>
                当前进度
              </Typography>
              <Typography variant="body2">
                当前局数：第 {matchState?.currentFrame.frameNumber} 局
                {matchState?.currentTurn.isBreakShot && ' (开球)'}
              </Typography>
              <Typography variant="body2">
                剩余球数：{matchState?.remainingBalls?.length || 0}
              </Typography>
              <Typography variant="body2">
                最小号球：{matchState?.remainingBalls && matchState.remainingBalls.length > 0 ? Math.min(...matchState.remainingBalls) : '无'}
              </Typography>
              {currentSpecialEvent && (
                <Typography
                  variant="body2"
                  sx={{
                    color: currentSpecialEvent.includes('黑') ? 'error.main' : 'success.main',
                    fontWeight: 'bold',
                    mt: 1
                  }}
                >
                  特殊事件：{currentSpecialEvent}
                  {currentSpecialEvent === '小金' && ' - 非开球轮清台小金球组!'}
                  {currentSpecialEvent === '黑小金' && ' - 非开球轮犯规清台小金球组!'}
                  {currentSpecialEvent === '大金' && ' - 开球轮清台奖励!'}
                  {currentSpecialEvent === '黑大金' && ' - 开球轮犯规清台惩罚!'}
                </Typography>
              )}
            </Paper>
          </Grid>

          {/* 规则设置 */}
          <Grid item xs={12} md={4}>
            <Paper variant="outlined" sx={{ p: 1.5 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, borderBottom: '1px solid rgba(0,0,0,0.1)', pb: 0.5 }}>
                规则设置
              </Typography>
              <Typography variant="body2">
                开球计分：{ruleConfig?.breakRules.breakShotPoints === 'double' ? '双倍计分' : '正常计分'}
              </Typography>
              <Typography variant="body2">
                清台分数：{ruleConfig?.specialWinConditions.slamPoints} 分
              </Typography>
              <Typography variant="body2">
                黄金9分数：{ruleConfig?.specialWinConditions.golden9Points} 分
              </Typography>
              <Typography variant="body2">
                罚分值：{ruleConfig?.foulRules.foulPenaltyPoints} 分
              </Typography>
              <Typography variant="body2">
                清台模式：{ruleConfig?.advancedOptions.clearTableMode === 'allGold' ? '全球金' : '1-9金'}
              </Typography>
            </Paper>
          </Grid>

          {/* 选手球组配置 */}
          <Grid item xs={12} md={6} mt={1}>
            <Paper variant="outlined" sx={{ p: 1.5 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, borderBottom: '1px solid rgba(0,0,0,0.1)', pb: 0.5 }}>
                选手球组配置
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                {playerConfigs.map((player) => (
                  <Box key={player.playerId} sx={{ minWidth: 200, p: 1, border: '1px solid #eee', borderRadius: 1, mb: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 'bold', borderBottom: '1px solid #eee', pb: 0.5, mb: 1 }}>
                      {player.playerName}:
                    </Typography>

                    {/* 球组配置 */}
                    <Typography variant="caption" sx={{ display: 'block', fontWeight: 'bold', color: 'text.secondary', mb: 0.5 }}>
                      球组配置:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                      {player.balls.map((ball) => (
                        <Paper
                          key={ball.id}
                          sx={{
                            width: 24,
                            height: 24,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '0.7rem',
                            bgcolor: 'grey.100',
                            position: 'relative',
                          }}
                        >
                          {ball.id}
                          <Typography
                            variant="caption"
                            sx={{
                              position: 'absolute',
                              top: -10,
                              right: -6,
                              fontSize: '0.6rem',
                              color: 'primary.main',
                              fontWeight: 'bold'
                            }}
                          >
                            {ball.points}
                          </Typography>
                        </Paper>
                      ))}
                    </Box>

                    {/* 小金球组 */}
                    <Typography variant="caption" sx={{ display: 'block', fontWeight: 'bold', color: 'warning.main', mb: 0.5, mt: 1 }}>
                      小金球组:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                      {player.slamConfig?.smallSlam?.balls?.map((ballId) => (
                        <Paper
                          key={`small-${ballId}`}
                          sx={{
                            width: 20,
                            height: 20,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '0.65rem',
                            bgcolor: 'warning.light',
                            color: 'warning.contrastText',
                          }}
                        >
                          {ballId}
                        </Paper>
                      )) || (
                        <Typography variant="caption" color="text.secondary">
                          未设置
                        </Typography>
                      )}
                    </Box>

                    {/* 大金球组 */}
                    <Typography variant="caption" sx={{ display: 'block', fontWeight: 'bold', color: 'error.main', mb: 0.5, mt: 1 }}>
                      大金球组:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                      {player.slamConfig?.bigSlam?.balls?.map((ballId) => (
                        <Paper
                          key={`big-${ballId}`}
                          sx={{
                            width: 20,
                            height: 20,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '0.65rem',
                            bgcolor: 'error.light',
                            color: 'error.contrastText',
                          }}
                        >
                          {ballId}
                        </Paper>
                      )) || (
                        <Typography variant="caption" color="text.secondary">
                          未设置
                        </Typography>
                      )}
                    </Box>

                    <Typography variant="body2" sx={{ mt: 1, fontSize: '0.75rem', color: 'text.secondary', borderTop: '1px solid #eee', pt: 0.5 }}>
                      总分值: {player.balls.reduce((sum, ball) => sum + ball.points, 0)} 分
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Paper>
          </Grid>

          {/* 特殊规则清台模式 */}
          <Grid item xs={12} md={6} mt={1}>
            <Paper variant="outlined" sx={{ p: 1.5 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, borderBottom: '1px solid rgba(0,0,0,0.1)', pb: 0.5 }}>
                特殊规则详情
              </Typography>

              {/* 大金规则 */}
              <Box sx={{ mb: 1.5 }}>
                <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                  大金规则 (开球轮清台):
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                  得分: 当前选手获得 {ruleConfig?.specialWinConditions.slamPoints * 2} 分
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                  罚分: 其他选手各扣除 {ruleConfig?.specialWinConditions.slamPoints} 分
                </Typography>
              </Box>

              {/* 小金规则 */}
              <Box sx={{ mb: 1.5 }}>
                <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                  小金规则 (非开球轮清台):
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                  得分: 当前选手获得 {ruleConfig?.specialWinConditions.slamPoints} 分
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                  罚分: 前序选手扣除 {ruleConfig?.specialWinConditions.slamPoints} 分
                </Typography>
              </Box>

              {/* 黄金9规则 */}
              <Box sx={{ mb: 1.5 }}>
                <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                  黄金9规则 (开球进9号球):
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                  得分: 当前选手获得 {ruleConfig?.specialWinConditions.golden9Points} 分
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                  罚分: 前序选手扣除 {ruleConfig?.specialWinConditions.golden9Points} 分
                </Typography>
              </Box>

              {/* 黑9规则 */}
              <Box sx={{ mb: 1.5 }}>
                <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'text.secondary' }}>
                  黑9规则 (非开球犯规进9号球):
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                  得分: 前序选手获得 {ruleConfig?.specialWinConditions.golden9Points} 分
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                  罚分: 当前选手扣除 {ruleConfig?.specialWinConditions.golden9Points} 分
                </Typography>
              </Box>

              {/* 黄金黑9规则 */}
              <Box sx={{ mb: 1.5 }}>
                <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'text.secondary' }}>
                  黄金黑9规则 (开球犯规进9号球):
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                  得分: 其他选手各获得 {ruleConfig?.specialWinConditions.golden9Points} 分
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                  罚分: 当前选手扣除 {ruleConfig?.specialWinConditions.golden9Points * 2} 分
                </Typography>
              </Box>

              {/* 黄金开球 */}
              {ruleConfig?.breakRules.breakShotPoints === 'double' && playerConfigs.length >= 3 && (
                <Box sx={{ mb: 1.5 }}>
                  <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                    黄金开球 (开球轮合法打进得分球):
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                    得分: 当前选手获得 进球总分值 * 2
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                    罚分: 其他选手各扣除 进球总分值
                  </Typography>
                </Box>
              )}

              {/* 黑金开球 */}
              {ruleConfig?.breakRules.breakShotPoints === 'double' && playerConfigs.length >= 3 && (
                <Box sx={{ mb: 1.5 }}>
                  <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'grey.700' }}>
                    黑金开球 (开球轮犯规打进得分球):
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                    得分: 其他选手各获得 进球总分值
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.8rem', ml: 2 }}>
                    罚分: 当前选手扣除 进球总分值 * 2
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Paper>

      {/* 选手信息和比分 */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {matchState?.players.map((player) => (
          <Grid item xs={12} md={4} key={player.id}>
            <Paper
              sx={{
                p: 2,
                bgcolor: player.role === 'current' ? 'primary.light' : 'background.paper',
                color: player.role === 'current' ? 'primary.contrastText' : 'text.primary',
                border: player.role === 'current' ? '2px solid' : 'none',
                borderColor: 'primary.main'
              }}
            >
              <Typography variant="h6">
                {player.name}
                <Box component="span" sx={{ ml: 1, fontSize: '0.8em' }}>
                  ({player.role === 'current' ? '当前' :
                    player.role === 'next' ? '下一位' : '上一位'})
                </Box>
              </Typography>
              <Typography>
                本局得分: {matchState?.currentFrame.scores[player.id]}
              </Typography>
              <Typography>
                总分: {matchState?.totalScores[player.id]}
              </Typography>

              {/* 显示特殊事件标记 - 使用SpecialEventManager */}
              {(() => {
                // 使用SpecialEventManager检查是否是特殊事件的触发者
                const isEventTrigger = SpecialEventManager.isEventTrigger(player.id, currentSpecialEventInfo);

                if (isEventTrigger) {
                  const displayConfig = SpecialEventManager.getEventDisplayConfig(currentSpecialEventInfo);

                  if (displayConfig.show) {
                    console.log(`选手 ${player.id} (${getPlayerName(player.id)}) 是${currentSpecialEventInfo.type}事件的触发者`);
                    return (
                      <Box sx={{
                        mt: 1,
                        p: 1,
                        bgcolor: `${displayConfig.color}.light`,
                        borderRadius: 1,
                        border: '2px solid',
                        borderColor: `${displayConfig.color}.dark`,
                      }}>
                        <Typography variant="subtitle2" sx={{
                          fontWeight: 'bold',
                          color: `${displayConfig.color}.contrastText`,
                          textAlign: 'center'
                        }}>
                          {displayConfig.text}
                        </Typography>
                      </Box>
                    );
                  }
                }

                return null;
              })()}
              {/* 显示该选手本局进球记录 */}
              <Box sx={{ mt: 1 }}>
                <Typography variant="subtitle2">本局进球：</Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                  {(() => {
                    // 调试日志
                    console.log(`显示选手 ${player.id} (${player.name}) 的进球记录`);

                    // 获取该选手的所有回合
                    const playerTurns = matchState?.currentFrame.turns.filter(turn => turn.playerId === player.id) || [];
                    console.log(`选手 ${player.id} 的回合数: ${playerTurns.length}`);
                    console.log('回合详情:', playerTurns);

                    // 获取该选手的所有进球
                    const playerBalls = playerTurns
                      .flatMap(turn => turn.shots || [])
                      .filter(shot => (shot.result === 'pot' || shot.result === 'foul') && shot.pottedBalls && shot.pottedBalls.length > 0)
                      .flatMap(shot => (shot.pottedBalls || []).map(ball => ({
                        ...ball,
                        isFoul: shot.result === 'foul'
                      })))
                      .sort((a, b) => a.id - b.id);

                    console.log(`选手 ${player.id} 的进球数: ${playerBalls.length}`);
                    console.log('进球详情:', playerBalls);

                    // 使用SpecialEventManager获取正确的进球记录
                    const finalBalls = SpecialEventManager.getPlayerBalls(
                      player.id,
                      playerBalls,
                      currentSpecialEventInfo
                    );

                    console.log(`选手 ${player.id} 最终进球记录:`, finalBalls);

                    // 如果没有进球，显示"无"
                    if (finalBalls.length === 0) {
                      return <Typography variant="body2" color="text.secondary">无</Typography>;
                    }

                    // 显示进球
                    return finalBalls.map((ball, index) => (
                      <Paper
                        key={`${ball.id}-${index}`}
                        sx={{
                          width: 24,
                          height: 24,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: '0.75rem',
                          bgcolor: ball.isFoul ? 'error.light' : 'grey.100',
                          color: ball.isFoul ? 'white' : 'text.primary',
                          border: ball.isFoul ? '1px solid' : '1px solid',
                          borderColor: ball.isFoul ? 'error.main' : 'grey.400',
                          fontWeight: 'bold'
                        }}
                      >
                        {ball.id}
                      </Paper>
                    ));
                  })()}
                </Box>
              </Box>
            </Paper>
          </Grid>
        ))}
      </Grid>

      {/* 本局记录 */}
      <Paper sx={{ p: 2, mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2 }}>
          本局记录
        </Typography>
        <Box sx={{ maxHeight: 200, overflowY: 'auto' }}>
          {/* 按照选手ID分组显示记录 */}
          {matchState?.players.map((player) => {
            // 获取该选手的所有回合
            // 使用Map来去除重复的回合，以回合索引为key
            const turnMap = new Map();
            matchState.currentFrame.turns.forEach((turn, index) => {
              if (turn.playerId === player.id) {
                // 使用回合的关键属性来生成唯一key
                const turnKey = `${turn.playerId}-${turn.isBreakShot ? 'break' : index}-${turn.shots.length}`;
                if (!turnMap.has(turnKey)) {
                  turnMap.set(turnKey, { ...turn, originalIndex: index });
                }
              }
            });

            // 将Map转换为数组，并按原始顺序排序
            const playerTurns = Array.from(turnMap.values())
              .sort((a, b) => a.originalIndex - b.originalIndex);

            // 如果该选手没有回合记录，则不显示
            if (playerTurns.length === 0) return null;

            return (
              <Box key={player.id} sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{
                  fontWeight: 'bold',
                  borderBottom: '1px solid',
                  borderColor: 'divider',
                  pb: 0.5,
                  mb: 1
                }}>
                  {player.name}：
                </Typography>

                {playerTurns.map((turn, turnIndex) => (
                  <Box key={turnIndex} sx={{ mb: 1, pl: 2 }}>
                    <Typography variant="body2" sx={{ fontStyle: 'italic', mb: 0.5 }}>
                      {turn.isBreakShot ? '开球轮' : `第 ${turnIndex + 1} 回合`}
                    </Typography>

                    <Box sx={{ pl: 2 }}>
                      {/* 将连续进球分组显示 */}
                      {(() => {
                        // 定义分组类型
                        type ShotGroup =
                          | { type: 'pot-group'; shots: { result: ShotResult; pottedBalls?: PottedBall[]; foulPoints?: number; }[] }
                          | { type: 'other'; shot: { result: ShotResult; pottedBalls?: PottedBall[]; foulPoints?: number; } };

                        // 将连续进球分组
                        const shotGroups: ShotGroup[] = [];
                        let currentGroup: { result: ShotResult; pottedBalls?: PottedBall[]; foulPoints?: number; }[] = [];



                        // 创建一个集合来跟踪已经处理过的球号
                        const processedBallIds = new Set<number>();

                        turn.shots.forEach((shot: { result: ShotResult; pottedBalls?: PottedBall[]; foulPoints?: number }) => {
                          // 使用SpecialEventManager处理进球记录
                          if ((shot.result === 'pot' || shot.result === 'foul') && shot.pottedBalls && shot.pottedBalls.length > 0) {
                            // 转换为标准格式
                            const standardBalls = shot.pottedBalls.map(ball => ({
                              id: ball.id,
                              points: ball.points,
                              isFoul: shot.result === 'foul'
                            }));

                            // 使用SpecialEventManager获取正确的进球记录
                            const filteredBalls = SpecialEventManager.getPlayerBalls(
                              player.id,
                              standardBalls,
                              currentSpecialEventInfo
                            );

                            if (filteredBalls.length > 0) {
                              // 进一步去重，确保同一个球不会在同一个回合中重复显示
                              const ballsToAdd = filteredBalls.filter(ball => {
                                if (!processedBallIds.has(ball.id)) {
                                  processedBallIds.add(ball.id);
                                  return true;
                                }
                                return false;
                              });

                              if (ballsToAdd.length > 0) {
                                const filteredShot = {
                                  ...shot,
                                  pottedBalls: ballsToAdd.map(ball => ({
                                    id: ball.id,
                                    points: ball.points
                                  }))
                                };
                                currentGroup.push(filteredShot);
                              }
                            }
                          } else {
                            // 处理非进球动作
                            if (currentGroup.length > 0) {
                              shotGroups.push({ type: 'pot-group', shots: [...currentGroup] });
                              currentGroup = [];
                            }
                            shotGroups.push({ type: 'other', shot });
                          }
                        });

                        // 如果还有未处理的进球组
                        if (currentGroup.length > 0) {
                          shotGroups.push({ type: 'pot-group', shots: [...currentGroup] });
                        }

                        return shotGroups.map((group, groupIndex) => {
                          if (group.type === 'pot-group') {
                            // 连续进球显示在同一行
                            // 将每个球与其犯规状态关联
                            const allPottedBalls = group.shots.flatMap(shot =>
                              (shot.pottedBalls || []).map(ball => ({
                                ...ball,
                                isFoul: shot.result === 'foul'
                              }))
                            );
                            return (
                              <Typography key={`group-${groupIndex}`} variant="body2" sx={{ mb: 1 }}>
                                进球：
                                <Box component="span" sx={{ display: 'inline-flex', flexWrap: 'wrap', gap: 0.5, ml: 1 }}>
                                  {allPottedBalls.map((ball, ballIdx) => (
                                    <Paper
                                      key={`ball-${ball.id}-${ballIdx}`}
                                      sx={{
                                        width: 24,
                                        height: 24,
                                        display: 'inline-flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        fontSize: '0.75rem',
                                        bgcolor: ball.isFoul ? 'error.light' : 'grey.100',
                                        color: ball.isFoul ? 'white' : 'text.primary',
                                        border: ball.isFoul ? '1px solid' : '1px solid',
                                        borderColor: ball.isFoul ? 'error.main' : 'grey.400',
                                        fontWeight: 'bold'
                                      }}
                                    >
                                      {ball.id}
                                    </Paper>
                                  ))}
                                </Box>
                              </Typography>
                            );
                          } else {
                            // 其他动作单独显示
                            const shot = group.shot;
                            return (
                              <Typography key={`other-${groupIndex}`} variant="body2" sx={{ mb: 1 }}>
                                {shot.result === 'foul' &&
                                  `犯规：-${shot.foulPoints} 分`}
                                {shot.result === 'miss' && '未进球'}
                                {shot.result === 'concede' && '让球'}
                              </Typography>
                            );
                          }
                        });
                      })()}
                    </Box>
                  </Box>
                ))}
              </Box>
            );
          })}
        </Box>
      </Paper>

      {/* 剩余球显示与进球操作区 - 改进版 */}
      <Paper sx={{ p: 2, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            剩余球 ({matchState?.remainingBalls?.length || 0})
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {/* 犯规复选框 */}
            <FormControlLabel
              control={
                <Checkbox
                  checked={isFoulChecked}
                  onChange={(e) => setIsFoulChecked(e.target.checked)}
                  color="error"
                />
              }
              label="犯规"
            />

            {/* 犯规分值已在规则配置中预先设置，不再需要选择 */}
          </Box>
        </Box>

        {/* 球选择区 */}
        <Box sx={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: 1,
          maxHeight: '200px',
          overflowY: 'auto',
          p: 1,
          mb: 2,
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 1
        }}>
          {matchState && matchState.remainingBalls.sort((a, b) => a - b).map((ballId) => (
            <Paper
              key={ballId}
              sx={{
                width: 40,
                height: 40,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                bgcolor: selectedBalls.includes(ballId)
                  ? 'primary.main'
                  : ballId === Math.min(...(matchState.remainingBalls || []))
                    ? 'warning.light'
                    : 'background.paper',
                color: selectedBalls.includes(ballId) ? 'white' : 'inherit',
                fontWeight: ballId === Math.min(...(matchState.remainingBalls || [])) ? 'bold' : 'normal',
                border: '1px solid',
                borderColor: selectedBalls.includes(ballId) ? 'primary.dark' : 'divider',
                '&:hover': {
                  bgcolor: selectedBalls.includes(ballId) ? 'primary.dark' : 'action.hover',
                  boxShadow: 1
                }
              }}
              onClick={() => {
                // 切换球的选中状态
                setSelectedBalls(prev =>
                  prev.includes(ballId)
                    ? prev.filter(id => id !== ballId)
                    : [...prev, ballId]
                );
              }}
            >
              {ballId}
            </Paper>
          ))}
        </Box>

        {/* 操作按钮区 */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 2 }}>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              color="secondary"
              onClick={() => setSelectedBalls([])}
              disabled={selectedBalls.length === 0}
              size="large"
            >
              清除选择
            </Button>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              color="warning"
              startIcon={<RemoveCircleOutlineIcon />}
              onClick={() => handleDirectMiss(isFoulChecked)}
              size="large"
            >
              未进球
            </Button>

            <Button
              variant="contained"
              color="primary"
              startIcon={<AddCircleOutlineIcon />}
              onClick={() => {
                if (selectedBalls.length > 0) {
                  // 处理进球操作
                  handleDirectPot(selectedBalls, isFoulChecked);
                } else {
                  alert('请选择要进的球');
                }
              }}
              size="large"
              sx={{ minWidth: 120 }}
            >
              确认进球
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* 当前选手提示 */}
      {matchState?.currentTurn.isBreakShot ? (
        <Alert severity="info" sx={{ mb: 4 }}>
          {matchState?.players.find(p => p.role === 'current')?.name} 开球
        </Alert>
      ) : (
        <Alert severity="info" sx={{ mb: 4 }}>
          {matchState?.players.find(p => p.role === 'current')?.name} 击打 {Math.min(...(matchState?.remainingBalls || []))} 号球
        </Alert>
      )}

      {/* 操作按钮 - 新设计 */}
      <Paper sx={{ p: 2, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            其他操作
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
          {/* 让球操作区 */}
          <Paper sx={{ p: 2, flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <Typography variant="subtitle1" sx={{ mb: 1 }}>让球</Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2, textAlign: 'center' }}>
              将击球权让给下一位选手
            </Typography>
            {currentGameState?.canLetShot ? (
              <Button
                variant="contained"
                color="secondary"
                size="large"
                fullWidth
                onClick={handleLetShot}
                startIcon={<SkipNextIcon />}
              >
                让球
              </Button>
            ) : (
              <Button
                variant="contained"
                color="warning"
                size="large"
                fullWidth
                onClick={() => handleDirectConcede()}
                startIcon={<SkipNextIcon />}
              >
                让球
              </Button>
            )}
          </Paper>

          {/* 犯规分值已在规则配置中预先设置，不再需要选择 */}
        </Box>
      </Paper>

      {/* 比赛结束对话框 */}
      <Dialog open={matchEndDialogOpen} maxWidth="md" fullWidth>
        <DialogTitle>比赛结束</DialogTitle>
        <DialogContent>
          <Box sx={{ p: 2 }}>
            <Typography variant="h5" gutterBottom align="center">
              最终比分
            </Typography>

            <Grid container spacing={2} sx={{ mb: 4 }}>
              {matchState?.players.map(player => (
                <Grid item xs={12} md={4} key={player.id}>
                  <Paper
                    sx={{
                      p: 2,
                      bgcolor: matchWinner === player.id ? 'success.light' : 'background.paper',
                      border: matchWinner === player.id ? '2px solid' : 'none',
                      borderColor: 'success.main'
                    }}
                  >
                    <Typography variant="h6">
                      {player.name}
                      {matchWinner === player.id && ' (获胜者)'}
                    </Typography>
                    <Typography variant="h4">
                      {matchState?.totalScores[player.id] || 0}
                    </Typography>
                    <Typography variant="body2">
                      胜局数: {
                        matchState?.frames.filter(frame => frame.winner === player.id).length || 0
                      }
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>

            <Typography variant="h6" gutterBottom>
              比赛统计
            </Typography>

            <Typography variant="body1">
              总局数: {matchState?.frames.length || 0}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => navigate('/')} startIcon={<HomeIcon />}>
            返回主页
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              // 使用相同配置重新开始
              navigate('/match-config');
            }}
          >
            再来一局
          </Button>
        </DialogActions>
      </Dialog>

      {/* 击球结果对话框 */}
      <Dialog open={shotDialogOpen} onClose={() => {
        setShotDialogOpen(false);
        setSelectedBalls([]);
        setCurrentAction(null);
      }}>
        <DialogTitle>
          {matchState?.currentTurn.isBreakShot ? '开球' : '击球'}结果
          {currentAction === 'pot' && ' - 选择进球'}
          {currentAction === 'foul' && ' - 输入犯规分数'}
          {currentAction === 'miss' && ' - 确认未进球'}
          {currentAction === 'concede' && ' - 确认让球'}
        </DialogTitle>
        <DialogContent>
          {currentAction === 'pot' && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ mb: 1 }}>选择进球：</Typography>
              <Grid container spacing={1}>
                {matchState?.remainingBalls.map((ballId) => (
                  <Grid item key={ballId}>
                    <Paper
                      sx={{
                        width: 40,
                        height: 40,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: 'pointer',
                        bgcolor: selectedBalls.includes(ballId) ? 'primary.light' : 'background.paper',
                        color: selectedBalls.includes(ballId) ? 'primary.contrastText' : 'text.primary'
                      }}
                      onClick={() => {
                        setSelectedBalls(prev =>
                          prev.includes(ballId)
                            ? prev.filter(id => id !== ballId)
                            : [...prev, ballId]
                        );
                      }}
                    >
                      {ballId}
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}
          {/* 犯规分值已在规则配置中预先设置，不再需要选择 */}
          {currentAction === 'miss' && (
            <Typography>
              确认当前选手未进球，将轮换到下一位选手。
            </Typography>
          )}
          {currentAction === 'concede' && (
            <Typography>
              确认当前选手让球，将轮换到下一位选手。
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setShotDialogOpen(false);
            setSelectedBalls([]);
            setCurrentAction(null);
          }}>
            取消
          </Button>
          <Button
            onClick={handleConfirmShot}
            disabled={
              (currentAction === 'pot' && selectedBalls.length === 0)
            }
            color="primary"
            variant="contained"
          >
            确认
          </Button>
        </DialogActions>
      </Dialog>

      {/* 局结束对话框 */}
      <Dialog
        open={frameEndDialogOpen}
        onClose={handleCloseFrameEndDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>本局结束</DialogTitle>
        <DialogContent>
          <Typography variant="h6" sx={{ mb: 2, textAlign: 'center' }}>
            {frameWinner !== null &&
              `${matchState?.players.find(p => p.id === frameWinner)?.name} 获胜！`}
          </Typography>

          <Box sx={{ my: 2, p: 2, border: '1px solid #eee', borderRadius: 1 }}>
            <Typography variant="h6" sx={{ mb: 1, borderBottom: '1px solid #eee', pb: 1 }}>
              本局记分
            </Typography>
            <Grid container spacing={1}>
              {matchState?.players.map(player => {
                // 使用最后一个完成的局的分数，而不是当前局的分数
                // 因为在局结束时，我们已经重置了 currentFrame 的分数为 0
                // 并将结束的局添加到 frames 数组中
                const lastFrameIndex = matchState?.frames.length - 1;
                const score = lastFrameIndex >= 0 ? (matchState?.frames[lastFrameIndex].scores[player.id] || 0) : 0;
                return (
                  <Grid item xs={12} key={player.id}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="body1">{player.name}:</Typography>
                      <Typography
                        variant="body1"
                        color={score > 0 ? 'success.main' : score < 0 ? 'error.main' : 'text.primary'}
                        fontWeight="bold"
                      >
                        {score > 0 ? '+' : ''}{score}
                      </Typography>
                    </Box>
                  </Grid>
                );
              })}
            </Grid>
          </Box>

          {/* 特殊事件显示 - 增强版 */}
          {frameEndSpecialEvent && (
            <Box sx={{
              my: 2,
              p: 2,
              border: '2px solid',
              borderColor: frameEndSpecialEvent?.includes('黑') ? 'error.main' : 'success.main',
              borderRadius: 2,
              bgcolor: frameEndSpecialEvent?.includes('黑') ? 'error.light' : 'success.light',
              boxShadow: 3
            }}>
              <Typography variant="h5" sx={{
                mb: 2,
                borderBottom: '2px solid',
                borderColor: frameEndSpecialEvent?.includes('黑') ? 'error.dark' : 'success.dark',
                pb: 1,
                color: frameEndSpecialEvent?.includes('黑') ? 'error.dark' : 'success.dark',
                fontWeight: 'bold',
                textAlign: 'center'
              }}>
                特殊事件: {frameEndSpecialEvent}
              </Typography>
              <Typography variant="body1" sx={{
                fontWeight: 'bold',
                fontSize: '1.2rem',
                textAlign: 'center',
                mb: 2
              }}>
                {frameEndSpecialEvent === '大金' && '开球轮清台，当前选手获胜！'}
                {frameEndSpecialEvent === '黑大金' && '开球轮犯规清台，当前选手被罚！'}
                {frameEndSpecialEvent === '黄金5' && '开球轮合法打进5号球，得分翻倍！继续本局！'}
                {frameEndSpecialEvent === '黑金5' && '开球轮犯规打进5号球，罚分翻倍！继续本局！'}
                {frameEndSpecialEvent?.includes('小金') && '当前选手获胜，前序选手为败者！'}
                {frameEndSpecialEvent?.includes('黑小金') && '前序选手获胜，当前选手为败者！'}
                {frameEndSpecialEvent?.includes('黄金9') && '开球轮打进9号球，当前选手获胜！'}
                {frameEndSpecialEvent === '黑9' && '非开球轮犯规打进9号球，前序选手获胜！'}
                {frameEndSpecialEvent === '黄金黑9' && '开球轮犯规打进9号球，当前选手扣除双倍分数，其他选手各得分！'}
                {frameEndSpecialEvent?.includes('黄金开球') && '开球轮合法打进得分球，得分翻倍！局结束！'}
                {frameEndSpecialEvent?.includes('黑金开球') && '开球轮犯规打进得分球，罚分翻倍！局结束！'}
                {frameEndSpecialEvent?.includes('赢得本局') && '当前选手合法打进9号球，获得本局胜利！'}
              </Typography>
              {pendingNextFrameState && (
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: 1,
                  mt: 2,
                  pt: 2,
                  borderTop: '1px solid',
                  borderColor: frameEndSpecialEvent?.includes('黑') ? 'error.dark' : 'success.dark',
                }}>
                  <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                    下一局击球顺序:
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Chip
                      label={`1. ${getPlayerName(pendingNextFrameState.currentPlayerId)}`}
                      color="primary"
                      variant="filled"
                      sx={{ fontWeight: 'bold' }}
                    />
                    <Chip
                      label={`2. ${getPlayerName(pendingNextFrameState.nextPlayerId)}`}
                      color="default"
                      variant="outlined"
                    />
                    <Chip
                      label={`3. ${getPlayerName(pendingNextFrameState.previousPlayerId)}`}
                      color="default"
                      variant="outlined"
                    />
                  </Box>
                </Box>
              )}
            </Box>
          )}

          <Box sx={{ my: 2, p: 2, border: '1px solid #eee', borderRadius: 1 }}>
            <Typography variant="h6" sx={{ mb: 1, borderBottom: '1px solid #eee', pb: 1 }}>
              总分统计
            </Typography>
            <Grid container spacing={1}>
              {matchState?.players.map(player => {
                const totalScore = matchState?.totalScores?.[player.id] || 0;
                return (
                  <Grid item xs={12} key={player.id}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="body1">{player.name}:</Typography>
                      <Typography
                        variant="body1"
                        color={totalScore > 0 ? 'success.main' : totalScore < 0 ? 'error.main' : 'text.primary'}
                        fontWeight="bold"
                      >
                        {totalScore > 0 ? '+' : ''}{totalScore}
                      </Typography>
                    </Box>
                  </Grid>
                );
              })}
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCloseFrameEndDialog}
            variant="contained"
            fullWidth
            color="primary"
          >
            继续下一局
          </Button>
        </DialogActions>
      </Dialog>

      {/* 比赛结束对话框 */}
      <Dialog
        open={matchEndDialogOpen}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>比赛结束</DialogTitle>
        <DialogContent>
          <Box sx={{ textAlign: 'center', py: 2 }}>
            <Typography variant="h5" sx={{ mb: 3 }}>
              {matchWinner !== null &&
                `${matchState?.players.find(p => p.id === matchWinner)?.name} 获得比赛胜利！`}
            </Typography>
            <Typography variant="h6" sx={{ mb: 2 }}>
              最终比分
            </Typography>
            {matchState?.players.map(player => (
              <Typography key={player.id} variant="body1" sx={{ mb: 1 }}>
                {player.name}: {matchState?.totalScores[player.id]}
              </Typography>
            ))}
            <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
              各局胜负
            </Typography>
            {matchState?.frames.map((frame, index) => (
              <Typography key={index} sx={{ mb: 1 }}>
                第 {index + 1} 局: {
                  frame.winner !== undefined
                    ? `${matchState?.players.find(p => p.id === frame.winner)?.name} 胜`
                    : '未完成'
                }
              </Typography>
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleReturnHome}
            variant="contained"
            color="primary"
            startIcon={<HomeIcon />}
          >
            返回首页
          </Button>
        </DialogActions>
      </Dialog>

      {/* 日志区域 */}
      <Grid item xs={12} md={4}>
        <Paper elevation={3} sx={{ height: 'calc(80vh - 100px)', overflow: 'auto', p: 2 }}>
          <Typography variant="h6" gutterBottom>
            比赛日志
          </Typography>
          <List dense>
            {logEntries.slice().reverse().map((entry, index) => (
              <ListItem key={index} disablePadding>
                <ListItemText
                  primary={entry}
                  primaryTypographyProps={{ style: { fontSize: '0.8rem' } }}
                />
              </ListItem>
            ))}
          </List>
        </Paper>
      </Grid>
    </Container>
  );
};

export default ScoringPage;
