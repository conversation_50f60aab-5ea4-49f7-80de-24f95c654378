import React from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import {
  Box,
  Card,
  CardContent,
  FormControl,
  Grid,
  MenuItem,
  Select,
  TextField,
  Typography,
  Chip,
  FormHelperText,
} from '@mui/material';

// 球组配置接口
export interface BallGroupConfig {
  totalBalls: number;
  playerGroups: {
    [playerId: string]: {
      balls: number[];
      pointValues: { [ballId: number]: number };
      endingBalls: number[];
    };
  };
}

// 表单验证规则
const validationSchema = yup.object().shape({
  totalBalls: yup
    .number()
    .required('请选择总球数')
    .min(3, '最少需要3个球')
    .max(9, '最多支持9个球'),
  playerGroups: yup.object().shape({
    // 动态验证规则会在组件内部根据选手数量生成
  }),
});

interface Props {
  playerIds: string[];
  onSubmit: (values: BallGroupConfig) => void;
  initialValues?: BallGroupConfig;
}

const BallGroupConfigForm: React.FC<Props> = ({ playerIds, onSubmit, initialValues }) => {
  // 生成默认值
  const generateDefaultValues = (): BallGroupConfig => {
    const defaultPlayerGroups: BallGroupConfig['playerGroups'] = {};
    playerIds.forEach(playerId => {
      defaultPlayerGroups[playerId] = {
        balls: [],
        pointValues: {},
        endingBalls: [],
      };
    });

    return {
      totalBalls: 9,
      playerGroups: defaultPlayerGroups,
    };
  };

  const formik = useFormik<BallGroupConfig>({
    initialValues: initialValues || generateDefaultValues(),
    validationSchema,
    onSubmit,
  });

  // 生成可选球号数组
  const getBallNumbers = () => {
    return Array.from({ length: formik.values.totalBalls }, (_, i) => i + 1);
  };

  // 处理球组选择变化
  const handleBallsChange = (playerId: string, balls: number[]) => {
    const newPlayerGroups = {
      ...formik.values.playerGroups,
      [playerId]: {
        ...formik.values.playerGroups[playerId],
        balls,
        // 更新分值对象，移除不在选中球组中的球的分值
        pointValues: Object.fromEntries(
          Object.entries(formik.values.playerGroups[playerId].pointValues)
            .filter(([ballId]) => balls.includes(Number(ballId)))
        ),
        // 更新结束球，移除不在选中球组中的球
        endingBalls: formik.values.playerGroups[playerId].endingBalls
          .filter(ballId => balls.includes(ballId)),
      },
    };
    formik.setFieldValue('playerGroups', newPlayerGroups);
  };

  // 处理分值变化
  const handlePointValueChange = (playerId: string, ballId: number, value: number) => {
    const newPointValues = {
      ...formik.values.playerGroups[playerId].pointValues,
      [ballId]: value,
    };
    formik.setFieldValue(`playerGroups.${playerId}.pointValues`, newPointValues);
  };

  // 处理结束球变化
  const handleEndingBallsChange = (playerId: string, ballId: number) => {
    const currentEndingBalls = formik.values.playerGroups[playerId].endingBalls;
    const newEndingBalls = currentEndingBalls.includes(ballId)
      ? currentEndingBalls.filter(id => id !== ballId)
      : [...currentEndingBalls, ballId];
    formik.setFieldValue(`playerGroups.${playerId}.endingBalls`, newEndingBalls);
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          球组配置
        </Typography>
        <Box component="form" onSubmit={formik.handleSubmit}>
          <Grid container spacing={3}>
            {/* 总球数选择 */}
            <Grid item xs={12}>
              <FormControl fullWidth error={formik.touched.totalBalls && Boolean(formik.errors.totalBalls)}>
                <Typography variant="subtitle1" gutterBottom>
                  总球数
                </Typography>
                <Select
                  name="totalBalls"
                  value={formik.values.totalBalls}
                  onChange={formik.handleChange}
                >
                  {[3, 4, 5, 6, 7, 8, 9].map(num => (
                    <MenuItem key={num} value={num}>
                      {num}球
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.totalBalls && formik.errors.totalBalls && (
                  <FormHelperText>{formik.errors.totalBalls}</FormHelperText>
                )}
              </FormControl>
            </Grid>

            {/* 每个选手的球组配置 */}
            {playerIds.map((playerId, index) => (
              <Grid item xs={12} key={playerId}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      选手 {index + 1} 球组设置
                    </Typography>
                    
                    {/* 球组选择 */}
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" gutterBottom>
                          选择球组
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {getBallNumbers().map(ballId => (
                            <Chip
                              key={ballId}
                              label={ballId}
                              color={formik.values.playerGroups[playerId].balls.includes(ballId) ? "primary" : "default"}
                              onClick={() => {
                                const currentBalls = formik.values.playerGroups[playerId].balls;
                                handleBallsChange(
                                  playerId,
                                  currentBalls.includes(ballId)
                                    ? currentBalls.filter(id => id !== ballId)
                                    : [...currentBalls, ballId].sort((a, b) => a - b)
                                );
                              }}
                            />
                          ))}
                        </Box>
                      </Grid>

                      {/* 分值设置 */}
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" gutterBottom>
                          分值设置
                        </Typography>
                        <Grid container spacing={2}>
                          {formik.values.playerGroups[playerId].balls.map(ballId => (
                            <Grid item xs={6} sm={4} md={3} key={ballId}>
                              <TextField
                                fullWidth
                                label={`${ballId}号球分值`}
                                type="number"
                                value={formik.values.playerGroups[playerId].pointValues[ballId] || ''}
                                onChange={(e) => handlePointValueChange(playerId, ballId, Number(e.target.value))}
                                InputProps={{ inputProps: { min: 0, max: 100 } }}
                              />
                            </Grid>
                          ))}
                        </Grid>
                      </Grid>

                      {/* 结束球设置 */}
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" gutterBottom>
                          结束球设置
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {formik.values.playerGroups[playerId].balls.map(ballId => (
                            <Chip
                              key={ballId}
                              label={`${ballId}号球`}
                              color={formik.values.playerGroups[playerId].endingBalls.includes(ballId) ? "secondary" : "default"}
                              onClick={() => handleEndingBallsChange(playerId, ballId)}
                            />
                          ))}
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
};

export default BallGroupConfigForm; 