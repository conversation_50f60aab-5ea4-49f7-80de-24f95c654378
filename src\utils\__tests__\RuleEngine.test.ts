import { RuleEngine, GameState } from '../RuleEngine';
import { RuleConfig } from '../../components/match-config/RuleConfigForm';
import { PlayerBallConfig } from '../../components/match-config/PlayerCard';

describe('RuleEngine - 基础功能测试', () => {
  // 基础配置
  const basicRuleConfig: RuleConfig = {
    foulRules: {
      allowPushAfterBlock: false,
      foulPenaltyPoints: 1,
      foulPointsHandler: 'opponent'
    },
    scoreBallDelay: 0,
    clearTableMode: 'allGold',
    breakShotPoints: 'normal',
  };

  // 三个选手的配置
  const playerConfigs: PlayerBallConfig[] = [
    { 
      playerId: 0, 
      playerName: '选手A', 
      name: 'A', 
      balls: [
        { id: 1, points: 1, isEnding: false }, 
        { id: 2, points: 1, isEnding: false }
      ] 
    },
    { 
      playerId: 1, 
      playerName: '选手B', 
      name: 'B', 
      balls: [
        { id: 1, points: 1, isEnding: false }, 
        { id: 2, points: 1, isEnding: false }
      ] 
    },
    { 
      playerId: 2, 
      playerName: '选手C', 
      name: 'C', 
      balls: [
        { id: 1, points: 1, isEnding: false }, 
        { id: 2, points: 1, isEnding: false }
      ] 
    },
  ];

  // 初始游戏状态
  const initialState: GameState = {
    currentPlayerId: 0,
    nextPlayerId: 1,
    previousPlayerId: 2,
    remainingBalls: [1, 2],
    currentMinBall: 1,
    isObstructed: false,
    canLetShot: false,
    isBreakShot: true,
    playerCount: 3,
  };

  let ruleEngine: RuleEngine;

  beforeEach(() => {
    ruleEngine = new RuleEngine(basicRuleConfig, playerConfigs, 3);
  });

  describe('基本得分计算', () => {
    test('正常进球得分', () => {
      const result = ruleEngine.evaluateTurn(
        { ...initialState, isBreakShot: false },
        [1],
        false
      );
      
      // 验证得分计算
      expect(result.scores[0]).toBe(1);  // 当前选手得1分
      expect(result.scores[2]).toBe(-1); // 前序选手扣1分
      expect(result.scores[1]).toBe(0);  // 后序选手分数不变
    });

    test('犯规进球得分', () => {
      const result = ruleEngine.evaluateTurn(
        { ...initialState, isBreakShot: false },
        [1],
        true
      );
      
      // 验证得分计算（包括犯规分）
      expect(result.scores[0]).toBe(-2); // 当前选手扣1分球分和1分犯规分
      expect(result.scores[2]).toBe(2);  // 前序选手得1分球分和1分犯规分
      expect(result.scores[1]).toBe(0);  // 后序选手分数不变
    });

    test('开球轮进球（黄金球）', () => {
      const result = ruleEngine.evaluateTurn(
        { ...initialState, isBreakShot: true },
        [1],
        false
      );
      
      // 验证黄金球得分
      expect(result.scores[0]).toBe(2);  // 当前选手得2分
      expect(result.scores[1]).toBe(-1); // 其他选手各扣1分
      expect(result.scores[2]).toBe(-1);
      expect(result.specialEvent).toBe('黄金');
    });

    test('开球轮犯规进球（黑金球）', () => {
      const result = ruleEngine.evaluateTurn(
        { ...initialState, isBreakShot: true },
        [1],
        true
      );
      
      // 验证黑金球得分
      expect(result.scores[0]).toBe(-2); // 当前选手扣2分
      expect(result.scores[1]).toBe(1);  // 其他选手各得1分
      expect(result.scores[2]).toBe(1);
      expect(result.specialEvent).toBe('黑金');
    });
  });

  describe('选手轮转', () => {
    test('未进球时的选手轮转', () => {
      const result = ruleEngine.evaluateTurn(initialState, [], false);
      const nextState = result.nextState;

      // 验证选手轮转
      expect(nextState.currentPlayerId).toBe(1);  // B变为当前选手
      expect(nextState.nextPlayerId).toBe(2);     // C变为后序选手
      expect(nextState.previousPlayerId).toBe(0); // A变为前序选手
      expect(nextState.isBreakShot).toBe(false); // 不再是开球轮
    });

    test('进球后继续击球', () => {
      const result = ruleEngine.evaluateTurn(initialState, [1], false);
      const nextState = result.nextState;

      // 验证选手顺序不变
      expect(nextState.currentPlayerId).toBe(0);  // A仍为当前选手
      expect(nextState.nextPlayerId).toBe(1);     // B仍为后序选手
      expect(nextState.previousPlayerId).toBe(2); // C仍为前序选手
    });

    test('犯规后的选手轮转', () => {
      const result = ruleEngine.evaluateTurn(initialState, [1], true);
      const nextState = result.nextState;

      // 验证选手轮转
      expect(nextState.currentPlayerId).toBe(1);  // B变为当前选手
      expect(nextState.nextPlayerId).toBe(2);     // C变为后序选手
      expect(nextState.previousPlayerId).toBe(0); // A变为前序选手
      expect(nextState.isBreakShot).toBe(false); // 不再是开球轮
    });
  });
}); 
