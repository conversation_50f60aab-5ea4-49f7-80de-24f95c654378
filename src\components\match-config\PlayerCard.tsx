import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Card, CardContent, TextField, Box, Chip, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, Button, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

// 球的基础配置接口
interface BallConfig {
  id: number;
  points: number;
  isEnding: boolean;
}

// 清台配置接口
interface SlamConfig {
  points: number;
  balls: number[];
}

export interface PlayerBallConfig {
  playerName: string;
  playerId: number;
  name: string;
  balls: BallConfig[];
  slamConfig?: {
    bigSlam: SlamConfig;
    smallSlam: SlamConfig;
  };
}

interface PlayerCardProps {
  config: PlayerBallConfig;
  totalBalls: number;
  onConfigChange: (config: PlayerBallConfig) => void;
}

// 基础球样式
const StyledChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
  width: '50px',
  height: '50px',
  borderRadius: '50%',
  cursor: 'pointer',
  position: 'relative',
}));

// 分数角标样式
const PointsBadge = styled('div')(({ theme }) => ({
  position: 'absolute',
  top: '-8px',
  right: '-8px',
  backgroundColor: theme.palette.error.main,
  color: theme.palette.error.contrastText,
  borderRadius: '12px',
  padding: '2px 4px',
  fontSize: '12px',
  minWidth: '16px',
  textAlign: 'center',
  fontWeight: 'bold',
  zIndex: 1,
}));

// 清台球样式
const SlamChip = styled(StyledChip)(({ theme }) => ({
  '&.slam-selected': {
    backgroundColor: theme.palette.success.main,
    color: theme.palette.success.contrastText,
    border: 'none',
  },
}));

export const PlayerCard: React.FC<PlayerCardProps> = ({ config, totalBalls, onConfigChange }) => {
  // 当前编辑的球ID
  const [editingBallId, setEditingBallId] = useState<number | null>(null);
  // 当前编辑的球分数
  const [editingPoints, setEditingPoints] = useState<number>(0);
  // 本地球配置
  const [localBalls, setLocalBalls] = useState<BallConfig[]>(config.balls);
  // 本地清台配置
  const [localSlamConfig, setLocalSlamConfig] = useState(config.slamConfig);
  // 本地选手名称
  const [localPlayerName, setLocalPlayerName] = useState<string>(config.playerName || '');
  
  // 使用 ref 跟踪组件是否已经挂载，避免初始渲染时的不必要更新
  const isMounted = useRef(false);
  // 使用 ref 跟踪上一次的 config 值，用于比较变化
  const prevConfigRef = useRef({ ...config, totalBalls });

  // 只在组件挂载时初始化本地状态，之后不再自动同步
  useEffect(() => {
    if (!isMounted.current) {
      setLocalBalls(config.balls);
      setLocalSlamConfig(config.slamConfig);
      setLocalPlayerName(config.playerName || '');
      isMounted.current = true;
    }
  }, []);

  // 只有当 config 从外部发生变化（不是由本组件引起的变化）时才更新本地状态
  useEffect(() => {
    // 比较当前 config 和上一次的 config
    const prevConfig = prevConfigRef.current;
    
    // 如果 playerId 变化了，说明整个 config 对象被替换，需要完全更新本地状态
    if (prevConfig.playerId !== config.playerId) {
      setLocalBalls(config.balls);
      setLocalSlamConfig(config.slamConfig);
      setLocalPlayerName(config.playerName || '');
    }
    
    // 更新 ref 以便下次比较
    prevConfigRef.current = { ...config, totalBalls };
  }, [config]);

  // 获取当前结束球
  const currentEndingBall = useMemo(() => 
    localBalls.find(ball => ball.isEnding), 
    [localBalls]
  );

  // 获取当前结束球号码
  const currentEndingBallId = useMemo(() => 
    currentEndingBall?.id || totalBalls,
    [currentEndingBall, totalBalls]
  );

  // 更新清台配置
  const updateSlamConfig = useCallback((endBallNumber: number) => {
    console.log('更新清台配置:', endBallNumber);
    const allBalls = Array.from({ length: endBallNumber }, (_, i) => i + 1);
    const newSlamConfig = {
      bigSlam: {
        points: config.slamConfig?.bigSlam?.points ?? 9,
        balls: allBalls
      },
      smallSlam: {
        points: config.slamConfig?.smallSlam?.points ?? 6,
        balls: allBalls
      }
    };
    
    setLocalSlamConfig(newSlamConfig);
    
    const newConfig: PlayerBallConfig = {
      ...config,
      slamConfig: newSlamConfig
    };
    onConfigChange(newConfig);
  }, [config, onConfigChange]);

  // 初始化球的配置
  useEffect(() => {
    // 跳过初始化，如果不是首次挂载或 totalBalls 变化
    if (isMounted.current && prevConfigRef.current.totalBalls === totalBalls) {
      return;
    }
    
    // 获取现有的结束球ID和分数配置
    const currentBallSettings = config.balls.reduce((acc, ball) => {
      acc[ball.id] = {
        points: ball.points,
        isEnding: ball.isEnding
      };
      return acc;
    }, {} as Record<number, { points: number, isEnding: boolean }>);
    
    // 创建新的球配置
    const newBalls = Array.from({ length: totalBalls }, (_, index) => {
      const id = index + 1;
      const existingSettings = currentBallSettings[id];
      return {
        id,
        points: existingSettings?.points || 0,
        isEnding: existingSettings?.isEnding || false
      };
    });
    
    // 查找结束球ID
    const endingBallId = newBalls.find(ball => ball.isEnding)?.id || totalBalls;
    
    // 更新清台配置
    const allBalls = Array.from({ length: endingBallId }, (_, i) => i + 1);
    const newSlamConfig = {
      bigSlam: {
        points: config.slamConfig?.bigSlam?.points ?? 9,
        balls: allBalls
      },
      smallSlam: {
        points: config.slamConfig?.smallSlam?.points ?? 6,
        balls: allBalls
      }
    };
    
    // 更新本地状态
    setLocalBalls(newBalls);
    setLocalSlamConfig(newSlamConfig);
    
    // 只有当 totalBalls 真正变化时，才更新父组件的配置
    if (prevConfigRef.current.totalBalls !== totalBalls) {
      // 更新配置
      onConfigChange({ 
        ...config, 
        balls: newBalls,
        slamConfig: newSlamConfig
      });
    }
    
    // 更新 prevConfigRef 以记录当前的 totalBalls
    prevConfigRef.current = {
      ...prevConfigRef.current,
      totalBalls: totalBalls
    };
  }, [totalBalls]);

  // 处理结束球变更
  const handleEndingBallChange = useCallback((event: SelectChangeEvent<string>) => {
    const endingBallId = event.target.value === '' ? null : Number(event.target.value);
    console.log('结束球变更:', endingBallId);
    
    // 更新球配置
    const newBalls = localBalls.map(ball => ({
      ...ball,
      isEnding: ball.id === endingBallId
    }));
    
    setLocalBalls(newBalls);
    
    // 更新清台配置
    const finalEndingBallId = endingBallId || totalBalls;
    const allBalls = Array.from({ length: finalEndingBallId }, (_, i) => i + 1);
    const newSlamConfig = {
      bigSlam: {
        points: localSlamConfig?.bigSlam?.points ?? 9,
        balls: allBalls
      },
      smallSlam: {
        points: localSlamConfig?.smallSlam?.points ?? 6,
        balls: allBalls
      }
    };
    
    setLocalSlamConfig(newSlamConfig);
    
    // 更新配置
    onConfigChange({ 
      ...config, 
      balls: newBalls,
      slamConfig: newSlamConfig
    });
  }, [localBalls, localSlamConfig, config, onConfigChange, totalBalls]);

  // 处理名称变更
  const handleNameChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setLocalPlayerName(event.target.value);
  }, []);

  // 触发配置变化的回调
  const triggerConfigChange = useCallback((currentName?: string) => {
    console.log(`%c[PlayerCard ${config.playerId}] triggerConfigChange START`, 'color: green');
    const finalName = typeof currentName === 'string' ? currentName : localPlayerName;
    console.log(`PlayerCard ${config.playerId} triggerConfigChange: Name='${finalName}'`);
    // Construct the updated config OBJECT based on the PlayerCard's current state (balls, slam, etc.)
    const updatedConfig: PlayerBallConfig = {
      ...config,
      // name: localPlayerName, // 移除旧的 name
      playerName: finalName, // 使用正确的 playerName 和最新的值
      balls: localBalls,
      slamConfig: localSlamConfig,
    };
    onConfigChange(updatedConfig); // Call the prop passed from PlayerGroupConfigForm
    console.log(`%c[PlayerCard ${config.playerId}] triggerConfigChange END`, 'color: green');
  }, [config, localPlayerName, onConfigChange, localBalls, localSlamConfig]);

  // 处理失焦事件，在失焦时将最终名称传递给父组件
  const handleNameBlur = useCallback(() => {
    console.log(`%c[PlayerCard ${config.playerId}] handleNameBlur START`, 'color: blue');
    console.log(`PlayerCard ${config.playerId} handleNameBlur: ${localPlayerName}`);
    // 确保即使名称为空也传递，以便父级知道最新状态
    triggerConfigChange(localPlayerName);
    console.log(`%c[PlayerCard ${config.playerId}] handleNameBlur END`, 'color: blue');
  }, [config, localPlayerName, triggerConfigChange]);

  // 处理球点击 - 开始编辑
  const handleBallClick = useCallback((ballId: number) => {
    console.log('球被点击:', ballId);
    const ball = localBalls.find(b => b.id === ballId);
    if (ball) {
      setEditingBallId(ballId);
      setEditingPoints(ball.points);
    }
  }, [localBalls]);

  // 处理分数输入变更
  const handlePointsChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const numValue = parseInt(value, 10) || 0;
    console.log('分数输入变更:', numValue);
    setEditingPoints(numValue);
  }, []);

  // 处理分数保存
  const handleSavePoints = useCallback(() => {
    if (editingBallId === null) return;
    
    console.log('保存分数:', editingBallId, editingPoints);
    const newBalls = localBalls.map(ball => 
      ball.id === editingBallId ? { ...ball, points: editingPoints } : ball
    );
    
    setLocalBalls(newBalls);
    onConfigChange({ ...config, balls: newBalls });
    setEditingBallId(null); // 关闭编辑模式
  }, [editingBallId, editingPoints, localBalls, config, onConfigChange]);

  // 处理取消编辑
  const handleCancelEdit = useCallback(() => {
    setEditingBallId(null);
  }, []);

  // 处理清台球组选择
  const handleSlamBallClick = useCallback((ballId: number, slamType: 'bigSlam' | 'smallSlam') => {
    console.log('清台球被点击:', ballId, slamType);
    
    if (!localSlamConfig) {
      // 如果 slamConfig 不存在，先初始化
      updateSlamConfig(currentEndingBallId);
      return;
    }
    
    // 检查球是否已经在清台球组中
    const isInSlamGroup = localSlamConfig[slamType].balls.includes(ballId);
    console.log(`球 ${ballId} ${isInSlamGroup ? '已在' : '不在'} ${slamType} 球组中`);
    
    // 更新清台球组
    let newBalls;
    if (isInSlamGroup) {
      // 如果已经在球组中，则移除
      newBalls = localSlamConfig[slamType].balls.filter(id => id !== ballId);
    } else {
      // 如果不在球组中，则添加
      newBalls = [...localSlamConfig[slamType].balls, ballId].sort((a, b) => a - b);
    }
    
    console.log(`更新后的 ${slamType} 球组:`, newBalls);
    
    const newSlamConfig = {
      ...localSlamConfig,
      [slamType]: {
        ...localSlamConfig[slamType],
        balls: newBalls
      }
    };
    
    setLocalSlamConfig(newSlamConfig);
    
    // 更新配置
    const newConfig: PlayerBallConfig = { 
      ...config, 
      slamConfig: newSlamConfig
    };
    
    onConfigChange(newConfig);
  }, [localSlamConfig, config, onConfigChange, currentEndingBallId, updateSlamConfig]);

  // 处理清台分数变更
  const handleSlamPointsChange = useCallback((slamType: 'bigSlam' | 'smallSlam', event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const points = Number(event.target.value) || 0;
    console.log('清台分数变更:', slamType, points);
    
    if (!localSlamConfig) {
      // 如果 slamConfig 不存在，先初始化
      updateSlamConfig(currentEndingBallId);
      return;
    }
    
    // 更新清台分数
    const newSlamConfig = {
      ...localSlamConfig,
      [slamType]: {
        ...localSlamConfig[slamType],
        points
      }
    };
    
    setLocalSlamConfig(newSlamConfig);
    
    // 更新配置
    const newConfig: PlayerBallConfig = {
      ...config,
      slamConfig: newSlamConfig
    };
    
    onConfigChange(newConfig);
  }, [localSlamConfig, config, onConfigChange, currentEndingBallId, updateSlamConfig]);

  // 球的渲染样式
  const getBallStyle = useCallback((ball: BallConfig) => ({
    margin: 0.5,
    width: '50px',
    height: '50px',
    borderRadius: '50%',
    cursor: 'pointer',
    backgroundColor: ball.isEnding ? '#f44336' : (ball.points > 0 ? '#1976d2' : 'transparent'),
    color: (ball.isEnding || ball.points > 0) ? '#fff' : 'inherit',
    border: (!ball.isEnding && ball.points === 0) ? '2px solid #bdbdbd' : 'none',
    '&:hover': {
      backgroundColor: ball.isEnding ? '#d32f2f' : (ball.points > 0 ? '#1565c0' : '#e0e0e0'),
    },
    opacity: ball.id > currentEndingBallId ? 0.5 : 1,
    pointerEvents: ball.id > currentEndingBallId ? 'none' : 'auto'
  }), [currentEndingBallId]);

  // 检查球是否在清台球组中
  const isBallInSlamGroup = useCallback((ballId: number, slamType: 'bigSlam' | 'smallSlam') => {
    if (!localSlamConfig) return false;
    return localSlamConfig[slamType].balls.includes(ballId);
  }, [localSlamConfig]);

  return (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            label="选手姓名"
            value={localPlayerName}
            onChange={handleNameChange}
            onBlur={handleNameBlur} // 添加 onBlur 事件处理器
          />
        </Box>

        {/* 球组设置 */}
        <Box sx={{ mb: 2 }}>
          <InputLabel>球组设置（点击球设置分值）</InputLabel>
          <Box sx={{ 
            display: 'flex', 
            flexWrap: 'wrap', 
            mt: 1,
            justifyContent: 'center',
            gap: 1
          }}>
            {localBalls.map((ball) => (
              <Box key={ball.id} sx={{ position: 'relative' }}>
                <Chip
                  label={ball.id}
                  onClick={() => handleBallClick(ball.id)}
                  sx={getBallStyle(ball)}
                />
                {ball.points > 0 && (
                  <PointsBadge>+{ball.points}</PointsBadge>
                )}
              </Box>
            ))}
          </Box>
        </Box>

        {/* 编辑球分数的界面 */}
        {editingBallId !== null && (
          <Box sx={{ mb: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
            <Typography variant="subtitle1" sx={{ mb: 1 }}>
              设置 {editingBallId} 号球分值
            </Typography>
            <TextField
              fullWidth
              type="number"
              value={editingPoints}
              onChange={handlePointsChange}
              inputProps={{ min: 0 }}
              autoFocus
              sx={{ mb: 2 }}
            />
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
              <Button variant="outlined" onClick={handleCancelEdit}>
                取消
              </Button>
              <Button variant="contained" onClick={handleSavePoints}>
                保存
              </Button>
            </Box>
          </Box>
        )}

        {/* 结束球选择 */}
        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>结束球</InputLabel>
          <Select
            value={currentEndingBall?.id?.toString() || ''}
            label="结束球"
            onChange={handleEndingBallChange}
          >
            <MenuItem value="">
              <em>无</em>
            </MenuItem>
            {localBalls.map((ball) => (
              <MenuItem key={ball.id} value={ball.id.toString()}>
                {ball.id} 号球
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* 清台球组设置 */}
        {localSlamConfig && (
          <>
            <Box sx={{ mb: 2, mt: 2 }}>
              <InputLabel>清台球组-大金</InputLabel>
              <Box sx={{ 
                display: 'flex', 
                flexWrap: 'wrap', 
                mt: 1,
                justifyContent: 'center',
                gap: 1
              }}>
                {localBalls.map((ball) => (
                  <SlamChip
                    key={`big-slam-${ball.id}`}
                    label={ball.id}
                    onClick={() => handleSlamBallClick(ball.id, 'bigSlam')}
                    className={isBallInSlamGroup(ball.id, 'bigSlam') ? 'slam-selected' : ''}
                    sx={{
                      opacity: ball.id > currentEndingBallId ? 0.5 : 1,
                      pointerEvents: ball.id > currentEndingBallId ? 'none' : 'auto'
                    }}
                  />
                ))}
              </Box>
              <TextField
                fullWidth
                label="大金分数"
                type="number"
                value={localSlamConfig.bigSlam.points}
                onChange={(e) => handleSlamPointsChange('bigSlam', e)}
                sx={{ mt: 1 }}
                inputProps={{ min: 0 }}
              />
            </Box>

            <Box sx={{ mb: 2 }}>
              <InputLabel>清台球组-小金</InputLabel>
              <Box sx={{ 
                display: 'flex', 
                flexWrap: 'wrap', 
                mt: 1,
                justifyContent: 'center',
                gap: 1
              }}>
                {localBalls.map((ball) => (
                  <SlamChip
                    key={`small-slam-${ball.id}`}
                    label={ball.id}
                    onClick={() => handleSlamBallClick(ball.id, 'smallSlam')}
                    className={isBallInSlamGroup(ball.id, 'smallSlam') ? 'slam-selected' : ''}
                    sx={{
                      opacity: ball.id > currentEndingBallId ? 0.5 : 1,
                      pointerEvents: ball.id > currentEndingBallId ? 'none' : 'auto'
                    }}
                  />
                ))}
              </Box>
              <TextField
                fullWidth
                label="小金分数"
                type="number"
                value={localSlamConfig.smallSlam.points}
                onChange={(e) => handleSlamPointsChange('smallSlam', e)}
                sx={{ mt: 1 }}
                inputProps={{ min: 0 }}
              />
            </Box>
          </>
        )}
      </CardContent>
    </Card>
  );
};