import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Box, Button, Stepper, Step, StepLabel } from '@mui/material';
import MatchBasicInfoForm, { MatchBasicInfo, MatchBasicInfoFormRef } from '../components/match-config/MatchBasicInfoForm';
import { PlayerGroupConfigForm, PlayerGroupConfigFormRef } from '../components/match-config/PlayerGroupConfigForm';
import { PlayerBallConfig } from '../types/playerConfig';
import RuleConfigForm, { RuleConfig } from '../components/match-config/RuleConfigForm';
import ConfigConfirmation from '../components/match-config/ConfigConfirmation';

// 配置步骤标题
const steps = ['基本信息配置', '球组配置', '规则配置', '确认配置'];

const MatchConfigPage: React.FC = () => {
  const navigate = useNavigate();
  // 当前步骤
  const [activeStep, setActiveStep] = useState(0);
  // 存储基本信息
  const [basicInfo, setBasicInfo] = useState<MatchBasicInfo | null>(null);
  // 存储球组配置
  const [playerConfigs, setPlayerConfigs] = useState<PlayerBallConfig[] | null>(null);
  // 存储临时球组配置
  const [tempPlayerConfigs, setTempPlayerConfigs] = useState<PlayerBallConfig[] | null>(null);
  // 存储规则配置
  const [ruleConfig, setRuleConfig] = useState<RuleConfig | null>(null);
  // 为球组配置表单创建 Ref
  const playerConfigFormRef = useRef<PlayerGroupConfigFormRef>(null);
  // 为基本信息表单创建 Ref
  const basicInfoFormRef = useRef<MatchBasicInfoFormRef>(null);

  // 添加调试日志，监控状态变化
  useEffect(() => {
    console.log('状态变化 - activeStep:', activeStep);
    console.log('状态变化 - playerConfigs:', playerConfigs);
    console.log('状态变化 - 下一步按钮是否禁用:', activeStep === 1 && (!playerConfigs || playerConfigs.length === 0));
  }, [activeStep, playerConfigs]);

  // 处理基本信息提交
  const handleBasicInfoSubmit = useCallback((values: MatchBasicInfo) => {
    console.log("[MatchConfigPage] handleBasicInfoSubmit called with:", values);
    // Check for valid info structure
    if (values && typeof values === 'object' && values.name && values.playerCount > 0) {
        setBasicInfo(values);
        // 从 MatchBasicInfo 生成初始 PlayerBallConfig
        const initialPlayerConfigs = Array.from({ length: values.playerCount }, (_, i) => ({
            playerId: i + 1,
            playerName: `选手 ${i + 1}`, // Default name
            balls: [], // Initially no balls assigned
            slamConfig: { isSlam: false } // Default slam config
        }));
        setPlayerConfigs(initialPlayerConfigs.map(config => ({
          ...config,
          name: config.playerName,
          slamConfig: {
            bigSlam: {
              points: 9,
              balls: []
            },
            smallSlam: {
              points: 6,
              balls: []
            }
          }
        })));
        console.log("[MatchConfigPage] Basic info set, initial player configs generated:", initialPlayerConfigs);
        setActiveStep(1); // Move to the next step AFTER basic info is successfully processed
    } else {
        console.error("[MatchConfigPage] Invalid basic info received:", values);
        alert("提交的基本信息无效，请检查表单。");
    }
  }, []); // No dependencies needed as setBasicInfo/setPlayerConfigs are stable

  // 检查是否有正在应用的模板
  useEffect(() => {
    console.log("[MatchConfigPage] activeStep changed to:", activeStep);
    if (activeStep === 1) {
      console.log("[MatchConfigPage] Checking for applying template...");

      // 先检查全局变量
      const globalTemplate = (window as any).currentApplyingTemplate;
      if (globalTemplate) {
        console.log("[MatchConfigPage] Found applying template in global variable:", globalTemplate);

        if (globalTemplate.playerConfigs) {
          console.log("[MatchConfigPage] Applying player configs from global:", globalTemplate.playerConfigs);

          // 深拷贝球组配置，并使用默认选手名称
          const playerConfigsCopy = JSON.parse(JSON.stringify(globalTemplate.playerConfigs));

          // 确保选手名称使用默认值
          playerConfigsCopy.forEach((player: any, index: number) => {
            player.playerName = `选手 ${index + 1}`;
            player.name = `选手 ${index + 1}`;
          });

          // 设置球组配置
          setTempPlayerConfigs(playerConfigsCopy);
          setPlayerConfigs(playerConfigsCopy);

          // 强制刷新球组配置表单
          setTimeout(() => {
            console.log("[MatchConfigPage] Force refreshing player configs form");
            const newConfigs = [...playerConfigsCopy];
            setPlayerConfigs(newConfigs);
          }, 100);
        }

        if (globalTemplate.ruleConfig) {
          console.log("[MatchConfigPage] Applying rule config from global:", globalTemplate.ruleConfig);
          // 设置规则配置
          setRuleConfig(globalTemplate.ruleConfig);
        }

        // 清除全局变量
        delete (window as any).currentApplyingTemplate;
        return;
      }

      // 如果全局变量中没有，则检查 sessionStorage
      const templateJson = sessionStorage.getItem('current_applying_template');
      console.log("[MatchConfigPage] Template JSON from sessionStorage:", templateJson);

      if (templateJson) {
        try {
          const config = JSON.parse(templateJson);
          console.log("[MatchConfigPage] Found applying template in sessionStorage:", config);

          if (config && config.playerConfigs) {
            console.log("[MatchConfigPage] Applying player configs from sessionStorage:", config.playerConfigs);

            // 深拷贝球组配置，并使用默认选手名称
            const playerConfigsCopy = JSON.parse(JSON.stringify(config.playerConfigs));

            // 确保选手名称使用默认值
            playerConfigsCopy.forEach((player: any, index: number) => {
              player.playerName = `选手 ${index + 1}`;
              player.name = `选手 ${index + 1}`;
            });

            // 设置球组配置
            setTempPlayerConfigs(playerConfigsCopy);
            setPlayerConfigs(playerConfigsCopy);

            // 强制刷新球组配置表单
            setTimeout(() => {
              console.log("[MatchConfigPage] Force refreshing player configs form");
              const newConfigs = [...playerConfigsCopy];
              setPlayerConfigs(newConfigs);
            }, 100);
          }

          if (config && config.ruleConfig) {
            console.log("[MatchConfigPage] Applying rule config from sessionStorage:", config.ruleConfig);
            // 设置规则配置
            setRuleConfig(config.ruleConfig);
          }

          // 清除应用中的模板
          sessionStorage.removeItem('current_applying_template');
        } catch (error) {
          console.error("[MatchConfigPage] Error parsing template:", error);
          sessionStorage.removeItem('current_applying_template');
        }
      }
    }
  }, [activeStep]);

  // 处理球组配置提交 - 使用 useCallback 包装
  const handlePlayerConfigSubmit = useCallback((configs: PlayerBallConfig[]) => {
    console.log('%c[MatchConfigPage] handlePlayerConfigSubmit START', 'color: purple; font-weight: bold');
    console.log('球组配置提交被调用，配置数据:', configs);
    if (configs && configs.length > 0) {
      // 使用收到的 configs 直接更新状态
      const newConfigs = configs;
      console.log('设置playerConfigs状态:', newConfigs);
      setPlayerConfigs(newConfigs);
      setTempPlayerConfigs(null);
      // 在状态更新后切换到下一步
      setActiveStep(2);
    } else {
      console.error('球组配置数据无效或为空');
      console.log('%c[MatchConfigPage] handlePlayerConfigSubmit END (Invalid Config)', 'color: purple; font-weight: bold');
      return; // 如果配置无效，提前返回
    }
    console.log('%c[MatchConfigPage] handlePlayerConfigSubmit END (Step set to 2)', 'color: purple; font-weight: bold');
  }, []); // 初始版本没有依赖项，因为 setPlayerConfigs 和 setActiveStep 是稳定的

  // 处理规则配置提交
  const handleRuleConfigSubmit = (config: RuleConfig) => {
    setRuleConfig(config);
    setActiveStep(3); // 进入确认页面
  };

  // 处理最终提交
  const handleFinalSubmit = () => {
    if (!basicInfo || !playerConfigs || !ruleConfig) {
      return;
    }

    // 整合所有配置数据
    const finalConfig = {
      basicInfo,
      playerConfigs,
      ruleConfig,
    };

    // 导航到计分界面，并传递配置数据
    navigate('/scoring', { state: finalConfig });
  };

  // 处理返回上一步
  const handleBack = () => {
    console.log('handleBack 被调用，当前步骤:', activeStep);

    setActiveStep((prevStep) => {
      // 如果从球组配置页面返回基本配置页面，将playerConfigs保存到tempPlayerConfigs
      if (prevStep === 1) {
        console.log('从球组配置页面返回基本配置页面，保存当前配置');
        // 保存当前配置而不是清空
        setTempPlayerConfigs(playerConfigs);
        // 在此不修改basicInfo，保持原样
      }
      return prevStep - 1;
    });
  };

  // 处理取消配置
  const handleCancel = () => {
    // 重置所有状态
    setActiveStep(0);
    setBasicInfo(null);
    setPlayerConfigs(null);
    setTempPlayerConfigs(null);
    setRuleConfig(null);
  };

  // 处理下一步
  const handleNext = () => {
    console.log('%c[MatchConfigPage] handleNext START (Current Step: %d)', 'color: teal', activeStep);
    console.log('当前状态 - playerConfigs:', playerConfigs);
    console.log('当前状态 - basicInfo:', basicInfo);

    if (activeStep === 0) {
      console.log("%c[MatchConfigPage] handleNext -> Calling submitForm() on MatchBasicInfoForm", 'color: teal');
      basicInfoFormRef.current?.submitForm(); // Trigger form submission via ref
      // Step progression is now handled inside handleBasicInfoSubmit
    } else if (activeStep === 1) {
      console.log('%c[MatchConfigPage] handleNext -> Calling submitForm() on PlayerGroupConfigForm', 'color: teal');
      playerConfigFormRef.current?.submitForm(); // Trigger form submission via ref
      // 状态更新和步骤切换将在 submitForm 内部调用的 onSubmit (即 handlePlayerConfigSubmit) 中处理
      console.log('%c[MatchConfigPage] handleNext END (submitForm called)', 'color: teal');
    } else if (activeStep === 2) {
      console.log('%c[MatchConfigPage] handleNext START (Current Step: 2)', 'color: teal');
      // 触发规则配置表单提交
      const form = document.querySelector('form');
      if (form) {
        form.dispatchEvent(new Event('submit', { bubbles: true }));
      }
      setActiveStep(3);
      console.log('%c[MatchConfigPage] handleNext END (Set Step to 3)', 'color: teal');
    } else if (activeStep === 3) {
      console.log('%c[MatchConfigPage] handleNext START (Current Step: 3)', 'color: teal');
      // 最终提交或导航
      console.log('完成所有配置，准备提交或开始比赛...');
      console.log('基本信息:', basicInfo);
      handleFinalSubmit();
    }
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ width: '100%', mt: 4 }}>
        {/* 步骤指示器 */}
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* 表单内容区 */}
        <Box sx={{ mt: 4, mb: 4 }}>
          {activeStep === 0 && (
            <MatchBasicInfoForm
              ref={basicInfoFormRef} // Pass the ref
              onSubmit={handleBasicInfoSubmit}
              initialValues={basicInfo || undefined} // Pass existing basicInfo if available
            />
          )}
          {activeStep === 1 && basicInfo && (
            <PlayerGroupConfigForm
              ref={playerConfigFormRef} // Pass the ref
              playerCount={basicInfo.playerCount}
              totalBalls={basicInfo.totalBalls}
              onSubmit={handlePlayerConfigSubmit as (configs: import("d:/MoonzWorkspace/BilliardsSore/src/components/match-config/PlayerCard").PlayerBallConfig[]) => void}
              initialConfigs={tempPlayerConfigs || playerConfigs}
            />
          )}
          {activeStep === 2 && (
            <RuleConfigForm
              onSubmit={handleRuleConfigSubmit}
              initialValues={ruleConfig || undefined}
            />
          )}
          {activeStep === 3 && basicInfo && playerConfigs && ruleConfig && (
            <ConfigConfirmation
              basicInfo={basicInfo}
              playerConfigs={playerConfigs}
              ruleConfig={ruleConfig}
            />
          )}
        </Box>

        {/* 导航按钮 */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          mt: 4,
          borderTop: '1px solid #e0e0e0',
          pt: 2
        }}>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleCancel}
          >
            取消
          </Button>
          <Box>
            <Button
              variant="outlined"
              color="primary"
              onClick={handleBack}
              disabled={activeStep === 0}
              sx={{ mr: 1 }}
            >
              上一步
            </Button>
            {activeStep < steps.length - 1 && (
              <Button
                variant="contained"
                color="primary"
                onClick={handleNext}
                disabled={activeStep === 1 && (!playerConfigs || playerConfigs.length === 0)}
                data-testid="next-button"
              >
                下一步
              </Button>
            )}
            {activeStep === steps.length - 1 && (
              <Button
                variant="contained"
                color="primary"
                onClick={handleFinalSubmit}
              >
                开始比赛
              </Button>
            )}
          </Box>
        </Box>
      </Box>
    </Container>
  );
};

export default MatchConfigPage;









