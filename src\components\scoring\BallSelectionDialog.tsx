import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Chip,
  FormControlLabel,
  Checkbox,
  Typography,
} from '@mui/material';

interface Props {
  open: boolean;
  onClose: () => void;
  onConfirm: (selectedBalls: number[], hasFoul: boolean) => void;
  remainingBalls: number[];
  currentMinBall: number;
}

const BallSelectionDialog: React.FC<Props> = ({
  open,
  onClose,
  onConfirm,
  remainingBalls,
  currentMinBall,
}) => {
  const [selectedBalls, setSelectedBalls] = useState<number[]>([]);
  const [hasFoul, setHasFoul] = useState(false);

  // 处理球的选择
  const handleBallClick = (ballId: number) => {
    setSelectedBalls((prev) => {
      if (prev.includes(ballId)) {
        return prev.filter((id) => id !== ballId);
      }
      return [...prev, ballId].sort((a, b) => a - b);
    });
  };

  // 处理确认
  const handleConfirm = () => {
    onConfirm(selectedBalls, hasFoul);
    handleClose();
  };

  // 处理关闭
  const handleClose = () => {
    setSelectedBalls([]);
    setHasFoul(false);
    onClose();
  };

  // 检查是否可以确认
  const canConfirm = () => {
    // 如果没有选择球但标记了犯规，允许确认
    if (hasFoul) {
      return true;
    }
    // 如果选择了球，不需要强制包含最小号球
    return selectedBalls.length > 0;
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>选择进球</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            当前最小球: {currentMinBall}号
            {selectedBalls.length > 0 && !selectedBalls.includes(currentMinBall) && (
              <Typography color="warning.main" sx={{ mt: 1 }}>
                提示：当前选择的球不包含最小球，可能会被判为犯规
              </Typography>
            )}
          </Typography>
          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 1,
              justifyContent: 'center',
              my: 2,
            }}
          >
            {remainingBalls.map((ballId) => (
              <Chip
                key={ballId}
                label={ballId}
                onClick={() => handleBallClick(ballId)}
                color={selectedBalls.includes(ballId) ? 'primary' : 'default'}
                variant={selectedBalls.includes(ballId) ? 'filled' : 'outlined'}
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  cursor: 'pointer',
                  bgcolor: ballId === currentMinBall ? 'warning.light' : undefined,
                }}
              />
            ))}
          </Box>
        </Box>

        <FormControlLabel
          control={
            <Checkbox
              checked={hasFoul}
              onChange={(e) => setHasFoul(e.target.checked)}
              color="error"
            />
          }
          label="同时犯规"
        />

        {selectedBalls.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" color="text.secondary">
              已选择的球: {selectedBalls.join(', ')}
            </Typography>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>取消</Button>
        <Button
          onClick={handleConfirm}
          color="primary"
          variant="contained"
          disabled={!canConfirm()}
        >
          确认
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BallSelectionDialog; 