# 台球计分系统 - 实现计划

## 1. 让球逻辑实现

### RuleEngine.ts 更新
```typescript
// 处理让球
handleLetShot(state: GameState): GameState {
  // 保存原始玩家顺序
  const originalPlayerOrder = [state.currentPlayerId, state.nextPlayerId, state.previousPlayerId];
  
  // 创建新状态
  const newState: GameState = {
    ...state,
    // 交换当前玩家和下一个玩家
    currentPlayerId: state.nextPlayerId,
    nextPlayerId: state.currentPlayerId,
    // 保存原始顺序以便后续恢复
    originalPlayerOrder: originalPlayerOrder,
    // 重置开球标志
    isBreakShot: false,
    // 重置让球标志
    canLetShot: false
  };
  
  return newState;
}
```

### ScoringPage.tsx 更新
```typescript
// 添加让球处理函数
const handleLetShot = () => {
  if (!ruleEngine || !currentGameState) return;
  
  try {
    // 调用规则引擎的让球处理
    const nextState = ruleEngine.handleLetShot(currentGameState);
    
    // 更新游戏状态
    setCurrentGameState(nextState);
    
    // 更新 UI 状态
    setMatchState(prev => {
      if (!prev) return null;
      
      // 更新选手角色
      const updatedPlayers = prev.players.map(player => ({
        ...player,
        role: player.id === nextState.currentPlayerId 
               ? 'current' as PlayerRole
               : player.id === nextState.nextPlayerId
                 ? 'next' as PlayerRole
                 : 'previous' as PlayerRole
      }));
      
      // 创建新的回合记录
      const newTurn: Turn = {
        playerId: nextState.currentPlayerId,
        isBreakShot: false,
        shots: []
      };
      
      // 在当前回合添加让球记录
      const updatedCurrentTurn: Turn = {
        ...prev.currentTurn,
        shots: [
          ...prev.currentTurn.shots,
          { result: 'concede' }
        ]
      };
      
      // 更新当前局
      const updatedFrame: Frame = {
        ...prev.currentFrame,
        turns: [...prev.currentFrame.turns, updatedCurrentTurn]
      };
      
      return {
        ...prev,
        currentFrame: updatedFrame,
        currentTurn: newTurn,
        players: updatedPlayers
      };
    });
    
    // 添加日志
    addLogEntry(`选手 ${getPlayerName(currentGameState.currentPlayerId)} 让球给选手 ${getPlayerName(nextState.currentPlayerId)}`);
    
  } catch (error) {
    console.error("处理让球时出错:", error);
    setErrorMessage("处理让球时发生错误: " + (error instanceof Error ? error.message : String(error)));
  }
};

// 在 UI 中添加让球按钮
{currentGameState?.canLetShot && (
  <Button
    variant="outlined"
    color="secondary"
    startIcon={<SkipNextIcon />}
    onClick={handleLetShot}
    sx={{ mr: 1 }}
  >
    让球
  </Button>
)}
```

## 2. 得分计算优化

### ScoringPage.tsx 更新
```typescript
// 更新分数辅助函数
const updateScores = (newScores: Record<number, number>) => {
  setMatchState(prev => {
    if (!prev) return null;
    
    // 更新当前局分数
    const updatedFrameScores = { ...prev.currentFrame.scores };
    
    // 合并新分数
    for (const [playerId, score] of Object.entries(newScores)) {
      const id = Number(playerId);
      updatedFrameScores[id] = (updatedFrameScores[id] || 0) + score;
    }
    
    // 更新当前局
    const updatedFrame: Frame = {
      ...prev.currentFrame,
      scores: updatedFrameScores
    };
    
    // 计算总分
    const updatedTotalScores = calculateTotalScores([...prev.frames, updatedFrame], playerConfigs);
    
    return {
      ...prev,
      currentFrame: updatedFrame,
      totalScores: updatedTotalScores
    };
  });
};

// 优化进球分数计算
const handleShotConfirm = () => {
  if (!currentAction || !ruleEngine || !currentGameState) return;
  
  const hasFoul = currentAction === 'foul' || 
                 (currentAction === 'pot' && foulPoints > 0);
  
  try {
    // 评估回合结果
    const result = ruleEngine.evaluateTurn(currentGameState, selectedBalls, hasFoul);
    
    // 更新分数
    updateScores(result.scores);
    
    // 更新进球记录，使用实际分数而非占位符
    const pottedBallsWithPoints = selectedBalls.map(ballId => {
      // 获取当前选手的球配置
      const playerConfig = playerConfigs.find(p => p.playerId === currentGameState.currentPlayerId);
      // 获取球的分值
      const ballConfig = playerConfig?.balls.find(b => b.id === ballId);
      return {
        id: ballId,
        points: ballConfig?.points || 0
      };
    });
    
    // 更新回合记录
    setMatchState(prev => {
      if (!prev) return null;
      
      const updatedShots = [...prev.currentTurn.shots, {
        result: currentAction,
        pottedBalls: currentAction === 'pot' ? pottedBallsWithPoints : undefined,
        foulPoints: hasFoul ? foulPoints : undefined
      }];
      
      return {
        ...prev,
        currentTurn: {
          ...prev.currentTurn,
          shots: updatedShots
        }
      };
    });
    
    // 其余逻辑保持不变...
  } catch (error) {
    // 错误处理...
  }
};
```

## 3. 小金逻辑完善

### RuleEngine.ts 更新
```typescript
// 在 evaluateTurn 方法中添加小金逻辑
evaluateTurn(state: GameState, pocketedBalls: number[], hasFoul: boolean): TurnResult {
  // 现有代码...
  
  // 检查是否清台
  if (pocketedBalls.length > 0 && state.remainingBalls.length === pocketedBalls.length) {
    // 已经是清台情况
    
    if (state.isBreakShot && this.ruleConfig.specialWinConditions.slamPoints) {
      // 大金：开球轮清台
      // 现有大金逻辑...
    } else if (!state.isBreakShot) {
      // 小金：非开球轮清台
      console.log('识别到小金事件: 非开球轮清台');
      
      // 获取当前选手的小金配置
      const currentPlayerConfig = this.playerConfigs.find(p => p.playerId === state.currentPlayerId);
      const smallSlamPoints = currentPlayerConfig?.slamConfig?.smallSlam?.points || 
                             this.ruleConfig.specialWinConditions.slamPoints || 0;
      
      if (smallSlamPoints > 0) {
        if (!hasFoul) {
          // 正常小金
          scores[state.currentPlayerId] = (scores[state.currentPlayerId] || 0) + smallSlamPoints;
          
          // 前序选手扣分
          if (state.previousPlayerId !== undefined && state.previousPlayerId !== null) {
            scores[state.previousPlayerId] = (scores[state.previousPlayerId] || 0) - smallSlamPoints;
          }
          
          specialEvent = '小金';
        } else {
          // 黑小金
          scores[state.currentPlayerId] = (scores[state.currentPlayerId] || 0) - smallSlamPoints;
          
          // 前序选手得分
          if (state.previousPlayerId !== undefined && state.previousPlayerId !== null) {
            scores[state.previousPlayerId] = (scores[state.previousPlayerId] || 0) + smallSlamPoints;
          }
          
          specialEvent = '黑小金';
        }
      }
    }
  }
  
  // 现有代码...
}
```

### ScoringPage.tsx 更新
```typescript
// 在特殊事件显示部分添加小金显示
{currentSpecialEvent && (
  <Typography 
    variant="body2" 
    sx={{ 
      color: currentSpecialEvent.includes('黑') ? 'error.main' : 'success.main', 
      fontWeight: 'bold',
      fontSize: '1.2rem',
      padding: '8px',
      border: '1px solid',
      borderColor: currentSpecialEvent.includes('黑') ? 'error.main' : 'success.main',
      borderRadius: '4px',
      mt: 1
    }}
  >
    特殊事件：{currentSpecialEvent}
    {currentSpecialEvent === '小金' && ' - 非开球轮清台奖励!'}
    {currentSpecialEvent === '黑小金' && ' - 非开球轮清台但最后一球犯规!'}
    {currentSpecialEvent === '大金' && ' - 开球轮清台奖励!'}
    {currentSpecialEvent === '黑大金' && ' - 开球轮清台但最后一球犯规!'}
  </Typography>
)}
```

## 4. 比赛结束判断

### RuleEngine.ts 更新
```typescript
// 完善比赛结束判断
checkMatchEnd(
  currentScores: Record<number, number>,
  currentFrameNumber: number,
  framesWon: Record<number, number>
): { isEnd: boolean; winnerId?: number; reason?: string } {
  // 按局数限制判断
  if (this.ruleConfig.endCondition === 'frames') {
    const totalFrames = this.ruleConfig.totalFrames;
    const winningFrames = this.ruleConfig.winningFrames;

    // 检查是否达到总局数
    if (totalFrames && currentFrameNumber >= totalFrames) {
      // 找出胜局数最多的选手
      let maxWins = 0;
      let winnerId: number | undefined;
      let isTie = false;
      
      for (const [id, wins] of Object.entries(framesWon)) {
        if (wins > maxWins) {
          maxWins = wins;
          winnerId = Number(id);
          isTie = false;
        } else if (wins === maxWins) {
          isTie = true;
        }
      }
      
      // 如果是平局且有决胜规则
      if (isTie && this.ruleConfig.tieBreaker) {
        if (this.ruleConfig.tieBreaker === 'highestScore') {
          // 按最高分决胜
          let maxScore = -Infinity;
          winnerId = undefined;
          
          for (const [id, score] of Object.entries(currentScores)) {
            if (score > maxScore) {
              maxScore = score;
              winnerId = Number(id);
            }
          }
          
          return { 
            isEnd: true, 
            winnerId, 
            reason: '已达到总局数，按最高分决胜' 
          };
        }
        // 其他决胜规则...
      }
      
      return { 
        isEnd: true, 
        winnerId: isTie ? undefined : winnerId, 
        reason: isTie ? '已达到总局数，平局' : '已达到总局数' 
      };
    }

    // 检查是否有人达到获胜局数
    if (winningFrames) {
      for (const [id, wins] of Object.entries(framesWon)) {
        if (wins >= winningFrames) {
          return { 
            isEnd: true, 
            winnerId: Number(id), 
            reason: `已达到获胜局数 (${winningFrames})` 
          };
        }
      }
    }
  }

  // 按分数判断
  if (this.ruleConfig.endCondition === 'score') {
    const targetScore = this.ruleConfig.targetScore;
    if (targetScore) {
      for (const [id, score] of Object.entries(currentScores)) {
        if (score >= targetScore) {
          return { 
            isEnd: true, 
            winnerId: Number(id), 
            reason: `已达到目标分数 (${targetScore})` 
          };
        }
      }
    }
  }
  
  return { isEnd: false };
}
```

### ScoringPage.tsx 更新
```typescript
// 在处理局结束时添加比赛结束检查
const handleFrameEnd = () => {
  if (!ruleEngine || !matchState) return;
  
  // 计算每个选手赢的局数
  const framesWon: Record<number, number> = {};
  matchState.players.forEach(player => {
    framesWon[player.id] = matchState.frames
      .filter(frame => frame.winner === player.id)
      .length;
  });
  
  // 检查比赛是否结束
  const matchEndStatus = ruleEngine.checkMatchEnd(
    matchState.totalScores,
    matchState.frames.length + 1, // 包括当前局
    framesWon
  );
  
  if (matchEndStatus.isEnd) {
    // 设置比赛获胜者
    setMatchWinner(matchEndStatus.winnerId || null);
    
    // 添加比赛结束日志
    addLogEntry(`比赛结束！${matchEndStatus.reason || ''}`);
    if (matchEndStatus.winnerId !== undefined) {
      addLogEntry(`获胜者: ${getPlayerName(matchEndStatus.winnerId)}`);
    } else {
      addLogEntry('比赛结果: 平局');
    }
    
    // 打开比赛结束对话框
    setMatchEndDialogOpen(true);
  } else {
    // 继续下一局
    startNextFrame();
  }
};

// 添加比赛结束对话框
<Dialog open={matchEndDialogOpen} maxWidth="md" fullWidth>
  <DialogTitle>比赛结束</DialogTitle>
  <DialogContent>
    <Box sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom align="center">
        最终比分
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {matchState?.players.map(player => (
          <Grid item xs={12} md={4} key={player.id}>
            <Paper 
              sx={{ 
                p: 2, 
                bgcolor: matchWinner === player.id ? 'success.light' : 'background.paper',
                border: matchWinner === player.id ? '2px solid' : 'none',
                borderColor: 'success.main'
              }}
            >
              <Typography variant="h6">
                {player.name}
                {matchWinner === player.id && ' (获胜者)'}
              </Typography>
              <Typography variant="h4">
                {matchState?.totalScores[player.id] || 0}
              </Typography>
              <Typography variant="body2">
                胜局数: {
                  matchState?.frames.filter(frame => frame.winner === player.id).length || 0
                }
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>
      
      <Typography variant="h6" gutterBottom>
        比赛统计
      </Typography>
      
      <Typography variant="body1">
        总局数: {matchState?.frames.length || 0}
      </Typography>
      
      <Typography variant="body1">
        比赛时长: {/* 计算比赛时长 */}
      </Typography>
      
      {/* 更多统计信息... */}
    </Box>
  </DialogContent>
  <DialogActions>
    <Button onClick={() => navigate('/')} startIcon={<HomeIcon />}>
      返回主页
    </Button>
    <Button 
      variant="contained" 
      color="primary" 
      onClick={() => {
        // 使用相同配置重新开始
        navigate('/scoring', { state: { basicInfo, playerConfigs, ruleConfig } });
      }}
    >
      再来一局
    </Button>
  </DialogActions>
</Dialog>
```
