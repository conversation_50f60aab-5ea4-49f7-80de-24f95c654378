import React from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  Radio,
  RadioGroup,
  Switch,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import { useFormik } from 'formik';
import * as yup from 'yup';

// 规则配置接口 old
//export interface RuleConfig {
//  allowPushAfterBlock: boolean;      // 是否允许让球
 // scoreBallDelay: number;            // 得分球延迟数量（0：不延迟，1：延一颗，2：延两颗）
//  clearTableMode: 'allGold' | '1-9'; // 清台模式（全球金/1-9金）
 // foulPoints: number;                // 犯规扣分数
//  foulPointsHandler: 'previousPlayer' | 'foulPool';  // 犯规分处理方式
//  breakShotPoints: 'normal' | 'double'; // 开球轮是否翻倍
//  slamPoints: number;                // 清台分数
 // golden9Points: number;             // 黄金9分数
 // endCondition: 'frames' | 'score';  // 比赛结束条件
//  totalFrames: number;               // 总局数
//  targetScore: number;               // 目标分数
//}
export interface RuleConfig {
  // 嵌套结构 (根据 PRD)
  breakRules: {
    breakShotPoints: 'normal' | 'double';
    // 如果 PRD 中还有其他开球规则，在此添加
  };
  foulRules: {
    allowPushAfterBlock: boolean; // 从顶层移入
    foulPenaltyPoints: number; // 新增：特定犯规的罚分值
    foulPointsHandler: 'opponent' | 'pool' | 'nextPlayer'; // 更新类型以匹配 RuleEngine 逻辑
    // 如果 PRD 中还有其他犯规规则 (例如击打错误球的处罚)，在此添加
  };
  specialWinConditions: {
    slamPoints: number; // 从顶层移入 (大金/小金)
    goldenBreakNineBall: boolean; // 新增：是否启用黄金9 (开球进9)
    blackNineBallLoss: boolean; // 新增：开球犯规进9是否算输 (黑9)
    golden9Points: number; // 从顶层移入 (黄金9得分)
    // 如果 PRD 中还有其他特殊胜利条件，在此添加
  };
  advancedOptions: {
    scoreBallDelay: number; // 从顶层移入
    clearTableMode: 'allGold' | '1-9'; // 从顶层移入
    // 如果 PRD 中还有其他高级选项，在此添加
  };

  // 顶层结束条件 (根据 PRD)
  endCondition: 'frames' | 'score' | 'time'; // 添加了 'time'
  totalFrames?: number; // 改为可选
  winningFrames?: number; // 新增：获胜局数，可选
  targetScore?: number; // 改为可选
  tieBreaker?: 'mostWins' | 'highestScore'; // 新增：决胜规则，可选

  // 注意：旧的顶层字段 foulPoints 可能已被 foulRules.foulPenaltyPoints 替代
  // 在确认其不再被使用前，暂时保留或注释掉
  // foulPoints: number;
}
// 验证规则
const validationSchema = yup.object().shape({
  breakRules: yup.object().shape({
    breakShotPoints: yup.string().oneOf(['normal', 'double'], '请选择开球计分方式').required('请选择开球计分方式'),
  }),
  foulRules: yup.object().shape({
    allowPushAfterBlock: yup.boolean().required('请选择是否允许让球'),
    foulPenaltyPoints: yup.number().integer('必须是整数').min(0, '不能小于0').required('请输入犯规罚分'),
    foulPointsHandler: yup.string().oneOf(['opponent', 'pool', 'nextPlayer'], '请选择犯规分处理方式').required('请选择犯规分处理方式'),
  }),
  specialWinConditions: yup.object().shape({
    slamPoints: yup.number().integer('必须是整数').min(0, '不能小于0').required('请输入清台分数'),
    goldenBreakNineBall: yup.boolean().required('请选择是否启用黄金9'),
    blackNineBallLoss: yup.boolean().required('请选择是否启用黑9规则'),
    golden9Points: yup.number().integer('必须是整数').min(0, '不能小于0').required('请输入黄金9分数')
  }),
  advancedOptions: yup.object().shape({
    scoreBallDelay: yup.number().integer('必须是整数').min(0, '不能小于0').max(2, '最多延迟2颗球').required('请选择得分球延迟数量'),
    clearTableMode: yup.string().oneOf(['allGold', '1-9'], '请选择清台模式').required('请选择清台模式')
  }),
  endCondition: yup.string().oneOf(['frames', 'score', 'time'], '请选择比赛结束条件').required('请选择比赛结束条件'),
  totalFrames: yup.number().when('endCondition', {
    is: 'frames',
    then: (schema) => schema.required('请设置总局数').integer('必须是整数').min(1, '总局数必须大于0'),
    otherwise: (schema) => schema.optional()
  }),
  winningFrames: yup.number().when(['endCondition', 'totalFrames'], {
    is: (endCondition: string, totalFrames?: number) => endCondition === 'frames' && typeof totalFrames === 'number' && totalFrames > 0,
    then: (schema) => schema.integer('必须是整数').min(1, '获胜局数必须大于0').max(yup.ref('totalFrames'), '获胜局数不能超过总局数'),
    otherwise: (schema) => schema.optional()
  }),
  targetScore: yup.number().when('endCondition', {
    is: 'score',
    then: (schema) => schema.required('请设置目标分数').integer('必须是整数').min(1, '目标分数必须大于0'),
    otherwise: (schema) => schema.optional()
  }),
  tieBreaker: yup.string().oneOf(['mostWins', 'highestScore'], '请选择有效的决胜规则').optional(),
});

interface Props {
  onSubmit: (values: RuleConfig) => void;
  initialValues?: RuleConfig;
}

const RuleConfigForm: React.FC<Props> = ({ onSubmit, initialValues }) => {
  const theme = useTheme();

  const formik = useFormik<RuleConfig>({
    initialValues: initialValues || {
      breakRules: {
        breakShotPoints: 'normal',  // 默认正常计分
      },
      foulRules: {
        allowPushAfterBlock: false,  // 默认不允许让球
        foulPenaltyPoints: 1,        // 默认罚1分
        foulPointsHandler: 'opponent',  // 默认加到对手分数
      },
      specialWinConditions: {
        slamPoints: 9,              // 默认清台9分
        goldenBreakNineBall: true,  // 默认启用黄金9
        blackNineBallLoss: true,    // 默认启用黑9规则
        golden9Points: 2,           // 默认黄金9得2分
      },
      advancedOptions: {
        scoreBallDelay: 0,          // 默认不延迟
        clearTableMode: 'allGold',   // 默认全球金
      },
      endCondition: 'frames',       // 默认按局数结束
      totalFrames: 10,              // 默认10局
      winningFrames: 6,             // 默认6局胜
      targetScore: 100,             // 默认100分（仅在分数模式下使用）
      tieBreaker: 'mostWins'        // 默认按胜局数决胜
    },
    validationSchema,
    onSubmit,
  });

  // 自定义单选组样式
  const radioGroupStyle = {
    '.MuiFormControlLabel-root': {
      marginY: 1,
      width: '100%',
      border: `1px solid ${theme.palette.divider}`,
      borderRadius: 1,
      padding: 2,
      transition: 'all 0.2s',
      '&:hover': {
        backgroundColor: theme.palette.action.hover,
      },
    },
    '.Mui-checked + .MuiFormControlLabel-label': {
      color: theme.palette.primary.main,
      fontWeight: 500,
    },
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          规则配置
        </Typography>
        <Box component="form" onSubmit={formik.handleSubmit}>
          {/* 遮挡球后是否允许让球 - 移至 foulRules */}
          <FormControl
            fullWidth
            error={formik.touched.foulRules?.allowPushAfterBlock && Boolean(formik.errors.foulRules?.allowPushAfterBlock)}
            sx={{ mb: 4 }}
          >
            <Typography variant="subtitle1" gutterBottom>
              遮挡球后是否允许让球
            </Typography>
            <RadioGroup
              name="foulRules.allowPushAfterBlock"
              value={formik.values.foulRules.allowPushAfterBlock}
              onChange={formik.handleChange}
              sx={radioGroupStyle}
            >
              <FormControlLabel
                value={true}
                control={<Radio />}
                label="允许让球"
              />
              <FormControlLabel
                value={false}
                control={<Radio />}
                label="不允许让球"
              />
            </RadioGroup>
            {formik.touched.foulRules?.allowPushAfterBlock && formik.errors.foulRules?.allowPushAfterBlock && (
              <FormHelperText>{String(formik.errors.foulRules.allowPushAfterBlock)}</FormHelperText>
            )}
          </FormControl>

          {/* 得分球延迟 - 移至 advancedOptions */}
          <FormControl
            fullWidth
            error={formik.touched.advancedOptions?.scoreBallDelay && Boolean(formik.errors.advancedOptions?.scoreBallDelay)}
            sx={{ mb: 4 }}
          >
            <Typography variant="subtitle1" gutterBottom>
              得分球传进延迟设置
            </Typography>
            <RadioGroup
              name="advancedOptions.scoreBallDelay"
              value={formik.values.advancedOptions.scoreBallDelay}
              onChange={formik.handleChange}
              sx={radioGroupStyle}
            >
              <FormControlLabel
                value={0}
                control={<Radio />}
                label="不延迟"
              />
              <FormControlLabel
                value={1}
                control={<Radio />}
                label="延一颗"
              />
              <FormControlLabel
                value={2}
                control={<Radio />}
                label="延两颗"
              />
            </RadioGroup>
            {formik.touched.advancedOptions?.scoreBallDelay && formik.errors.advancedOptions?.scoreBallDelay && (
              <FormHelperText>{String(formik.errors.advancedOptions.scoreBallDelay)}</FormHelperText>
            )}
          </FormControl>

          {/* 清台模式选择 - 移至 advancedOptions */}
          <FormControl
            fullWidth
            error={formik.touched.advancedOptions?.clearTableMode && Boolean(formik.errors.advancedOptions?.clearTableMode)}
            sx={{ mb: 4 }}
          >
            <Typography variant="subtitle1" gutterBottom>
              清台模式
            </Typography>
            <RadioGroup
              name="advancedOptions.clearTableMode"
              value={formik.values.advancedOptions.clearTableMode}
              onChange={formik.handleChange}
              sx={radioGroupStyle}
            >
              <FormControlLabel
                value="allGold"
                control={<Radio />}
                label="全球金"
              />
              <FormControlLabel
                value="1-9"
                control={<Radio />}
                label="1-9金"
              />
            </RadioGroup>
            {formik.touched.advancedOptions?.clearTableMode && formik.errors.advancedOptions?.clearTableMode && (
              <FormHelperText>{String(formik.errors.advancedOptions.clearTableMode)}</FormHelperText>
            )}
          </FormControl>

          {/* 犯规罚分值 - 移至 foulRules.foulPenaltyPoints */}
          <FormControl
            fullWidth
            error={formik.touched.foulRules?.foulPenaltyPoints && Boolean(formik.errors.foulRules?.foulPenaltyPoints)}
            sx={{ mb: 4 }}
          >
            <Typography variant="subtitle1" gutterBottom>
              犯规罚分值
            </Typography>
            <RadioGroup
              name="foulRules.foulPenaltyPoints"
              value={formik.values.foulRules.foulPenaltyPoints}
              onChange={formik.handleChange}
              sx={radioGroupStyle}
            >
              <FormControlLabel
                value={1}
                control={<Radio />}
                label="1分"
              />
              <FormControlLabel
                value={2}
                control={<Radio />}
                label="2分"
              />
              <FormControlLabel
                value={3}
                control={<Radio />}
                label="3分"
              />
            </RadioGroup>
            {formik.touched.foulRules?.foulPenaltyPoints && formik.errors.foulRules?.foulPenaltyPoints && (
              <FormHelperText>{String(formik.errors.foulRules.foulPenaltyPoints)}</FormHelperText>
            )}
          </FormControl>

          {/* 犯规罚分处理方式 - 移至 foulRules */}
          <FormControl
            fullWidth
            error={formik.touched.foulRules?.foulPointsHandler && Boolean(formik.errors.foulRules?.foulPointsHandler)}
            sx={{ mb: 4 }}
          >
            <Typography variant="subtitle1" gutterBottom>
              犯规罚分处理方式
            </Typography>
            <RadioGroup
              name="foulRules.foulPointsHandler"
              value={formik.values.foulRules.foulPointsHandler}
              onChange={formik.handleChange}
              sx={radioGroupStyle}
            >
              <FormControlLabel
                value="opponent"
                control={<Radio />}
                label="加到对手分数"
              />
              <FormControlLabel
                value="pool"
                control={<Radio />}
                label="累计到犯规池"
              />
              <FormControlLabel
                value="nextPlayer"
                control={<Radio />}
                label="加到下一家分数"
              />
            </RadioGroup>
            {formik.touched.foulRules?.foulPointsHandler && formik.errors.foulRules?.foulPointsHandler && (
              <FormHelperText>{String(formik.errors.foulRules.foulPointsHandler)}</FormHelperText>
            )}
          </FormControl>

          {/* 开球轮分数加倍选项 - 移至 breakRules */}
          <FormControl
            fullWidth
            error={formik.touched.breakRules?.breakShotPoints && Boolean(formik.errors.breakRules?.breakShotPoints)}
            sx={{ mb: 4 }}
          >
            <Typography variant="subtitle1" gutterBottom>
              开球轮进球分数
            </Typography>
            <RadioGroup
              name="breakRules.breakShotPoints"
              value={formik.values.breakRules.breakShotPoints}
              onChange={formik.handleChange}
              sx={radioGroupStyle}
            >
              <FormControlLabel
                value="normal"
                control={<Radio />}
                label="正常计分"
              />
              <FormControlLabel
                value="double"
                control={<Radio />}
                label="分数加倍"
              />
            </RadioGroup>
            {formik.touched.breakRules?.breakShotPoints && formik.errors.breakRules?.breakShotPoints && (
              <FormHelperText>{String(formik.errors.breakRules.breakShotPoints)}</FormHelperText>
            )}
          </FormControl>

          {/* 清台分数已在球组配置中设置，这里不再重复设置 */}

          <FormControl
            fullWidth
            error={formik.touched.specialWinConditions?.goldenBreakNineBall && Boolean(formik.errors.specialWinConditions?.goldenBreakNineBall)}
            sx={{ mb: 4 }}
          >
            <Typography variant="subtitle1" gutterBottom>
              黄金9设置
            </Typography>
            <FormGroup>
              <FormControlLabel
                control={
                  <Switch
                    name="specialWinConditions.goldenBreakNineBall"
                    checked={formik.values.specialWinConditions.goldenBreakNineBall}
                    onChange={formik.handleChange}
                  />
                }
                label="启用黄金9（开球进9）"
              />
              <FormControlLabel
                control={
                  <Switch
                    name="specialWinConditions.blackNineBallLoss"
                    checked={formik.values.specialWinConditions.blackNineBallLoss}
                    onChange={formik.handleChange}
                  />
                }
                label="开球犯规进9算输（黑9）"
              />
            </FormGroup>
          </FormControl>

          <FormControl
            fullWidth
            error={formik.touched.specialWinConditions?.golden9Points && Boolean(formik.errors.specialWinConditions?.golden9Points)}
            sx={{ mb: 4 }}
          >
            <Typography variant="subtitle1" gutterBottom>
              黄金9分数
            </Typography>
            <TextField
              fullWidth
              name="specialWinConditions.golden9Points"
              value={formik.values.specialWinConditions.golden9Points}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              type="number"
              error={formik.touched.specialWinConditions?.golden9Points && Boolean(formik.errors.specialWinConditions?.golden9Points)}
              helperText={formik.touched.specialWinConditions?.golden9Points && formik.errors.specialWinConditions?.golden9Points}
            />
          </FormControl>

          {/* 比赛结束条件、总局数、获胜局数和目标分数已在基本信息配置中设置，这里不再重复设置 */}

          {/* 决胜规则 */}
          <FormControl
            fullWidth
            error={formik.touched.tieBreaker && Boolean(formik.errors.tieBreaker)}
            sx={{ mb: 4 }}
          >
            <Typography variant="subtitle1" gutterBottom>
              决胜规则（可选）
            </Typography>
            <RadioGroup
              name="tieBreaker"
              value={formik.values.tieBreaker}
              onChange={formik.handleChange}
              sx={radioGroupStyle}
            >
              <FormControlLabel
                value="mostWins"
                control={<Radio />}
                label="胜局数多者获胜"
              />
              <FormControlLabel
                value="highestScore"
                control={<Radio />}
                label="总分高者获胜"
              />
            </RadioGroup>
            {formik.touched.tieBreaker && formik.errors.tieBreaker && (
              <FormHelperText>{String(formik.errors.tieBreaker)}</FormHelperText>
            )}
          </FormControl>

        </Box>
        <Box sx={{ display: 'none' }}>
          <Button type="submit">下一步</Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default RuleConfigForm;
