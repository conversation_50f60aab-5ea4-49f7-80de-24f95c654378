import { useCallback, useMemo, forwardRef, useImperativeHandle, useState, useEffect } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  Box,
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  Radio,
  RadioGroup,
  TextField,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Divider,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  IconButton
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
// 使用 Grid 组件
import Grid from '@mui/material/Grid';
import { TemplateService, Template } from '../../services/TemplateService';

// 比赛模式类型定义
export type MatchMode = 'time' | 'score' | 'frame' | 'manual';

// 基本信息表单数据接口
export interface MatchBasicInfo {
  name: string;
  date: string;
  location: string;
  matchType: 'manual' | 'auto';
  mode: 'time' | 'score' | 'frame';
  playerCount: number;
  totalBalls: number;
  playerNames: string[];
  settings: {
    time?: {
      duration: number;
      extraTime: number;
    };
    score?: {
      targetScore: number;
      minFrames?: number;
    };
    frame?: {
      totalFrames: number;
      winningFrames: number;
    };
  };
}

interface Props {
  onSubmit: (values: MatchBasicInfo) => void;
  initialValues?: MatchBasicInfo;
}

// 定义 Ref 暴露的方法类型
export interface MatchBasicInfoFormRef {
  submitForm: () => void;
  applyFullTemplate?: (template: Template) => void; // 新增方法用于应用完整模板
}

// 创建验证模式
const validationSchema = Yup.object({
  name: Yup.string().required('比赛名称必填'),
  date: Yup.string().required('比赛日期必填'),
  location: Yup.string().required('比赛地点必填'),
  matchType: Yup.string().oneOf(['manual', 'auto']).required('比赛类型必填'),
  mode: Yup.string().oneOf(['time', 'score', 'frame']).required('比赛模式必填'),
  playerCount: Yup.number()
    .min(2, '至少需要2名选手')
    .max(8, '最多支持8名选手')
    .required('选手数量必填'),
  totalBalls: Yup.number()
    .min(1, '至少需要1个球')
    .max(15, '最多支持15个球')
    .required('球数必填'),
  settings: Yup.object().when('mode', {
    is: (val: MatchMode) => val === 'time', // 使用函数形式确保类型正确
    then: (schema) => schema.shape({        // 使用函数式 then
      time: Yup.object({
        duration: Yup.number().min(1, '时长必须大于0').required('时长必填'),
        extraTime: Yup.number().min(0, '加时不能为负').required('加时必填')
      }).required() // 当模式为 time 时，time 设置是必需的
    }),
    // 可以为 score 和 frame 添加类似的 otherwises
    // otherwise: (schema) => schema.shape({...})
    otherwise: (schema) => schema // 默认情况下，保持 schema 不变
  })
});

// 创建默认初始值
const defaultInitialValues: MatchBasicInfo = {
  name: '',
  date: new Date().toISOString().split('T')[0],
  location: '',
  matchType: 'manual',
  mode: 'frame', // 默认使用定局模式
  playerCount: 3, // 默认 3 人
  totalBalls: 9,  // 默认 9 球
  playerNames: [],
  settings: {
    time: {
      duration: 60,
      extraTime: 30
    },
    score: {
      targetScore: 100,  // 默认目标分数 100
      minFrames: 1       // 默认最少局数 1
    },
    frame: {
      totalFrames: 10,    // 默认总局数 10
      winningFrames: 6    // 默认获胜局数 6
    }
  }
};

const MatchBasicInfoForm = forwardRef<MatchBasicInfoFormRef, Props>(({ onSubmit, initialValues }, ref) => {
  // 模板列表状态
  const [templates, setTemplates] = useState<Template[]>([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState<Template | null>(null);

  // 使用 useCallback 优化表单提交处理函数
  const handleSubmit = useCallback((values: MatchBasicInfo) => {
    console.log("[MatchBasicInfoForm] Form submitted successfully with values:", values);
    onSubmit(values); // 调用父组件传递的 onSubmit
  }, [onSubmit]);

  // 使用 useMemo 优化表单配置
  const formik = useFormik({
    initialValues: initialValues || defaultInitialValues,
    validationSchema,
    onSubmit: handleSubmit,
    enableReinitialize: false // 防止不必要的重新初始化
  });

  // 加载模板列表
  useEffect(() => {
    const loadedTemplates = TemplateService.getAllTemplates();
    setTemplates(loadedTemplates);
  }, []);

  // 处理删除模板
  const handleDeleteTemplate = useCallback((template: Template) => {
    setTemplateToDelete(template);
    setDeleteDialogOpen(true);
  }, []);

  // 确认删除模板
  const confirmDeleteTemplate = useCallback(() => {
    if (templateToDelete) {
      TemplateService.deleteTemplate(templateToDelete.id);
      // 重新加载模板列表
      const loadedTemplates = TemplateService.getAllTemplates();
      setTemplates(loadedTemplates);
    }
    setDeleteDialogOpen(false);
    setTemplateToDelete(null);
  }, [templateToDelete]);

  // 取消删除模板
  const cancelDeleteTemplate = useCallback(() => {
    setDeleteDialogOpen(false);
    setTemplateToDelete(null);
  }, []);

  // 应用模板
  const applyTemplate = useCallback((template: Template) => {
    console.log('应用模板:', template.name);
    console.log('模板内容:', template.config);

    // 创建模板配置的深拷贝
    const configCopy = JSON.parse(JSON.stringify(template.config));

    // 将模板数据存储到 sessionStorage（比 localStorage 更可靠）
    try {
      // 先清除之前的模板数据
      sessionStorage.removeItem('current_applying_template');

      // 然后存储新的模板数据
      const templateJson = JSON.stringify(configCopy);
      console.log('存储到 sessionStorage 的模板数据:', templateJson);
      sessionStorage.setItem('current_applying_template', templateJson);

      // 为了确保数据成功存储，立即读取并验证
      const storedJson = sessionStorage.getItem('current_applying_template');
      console.log('验证存储的模板数据:', storedJson);
      if (!storedJson) {
        console.error('模板数据存储失败！');
      }

      // 在全局变量中也存储一份，以防 sessionStorage 失效
      (window as any).currentApplyingTemplate = configCopy;
    } catch (error) {
      console.error('存储模板数据到 sessionStorage 时出错:', error);
    }

    // 设置表单值，但保留当前的可变信息
    // 使用类型断言确保 TypeScript 能够识别这些属性
    const basicInfo = template.config.basicInfo as MatchBasicInfo;
    const newValues: MatchBasicInfo = {
      ...basicInfo,
      // 保留空值，不设置默认值
      name: '',
      date: new Date().toISOString().split('T')[0], // 日期需要有值才能通过验证
      location: ''
    };

    // 确保必要的字段有值
    if (newValues.playerCount === undefined || newValues.playerCount < 2) {
      newValues.playerCount = 3; // 默认3人
    }

    // 确保总球数有值
    if (!newValues.totalBalls || newValues.totalBalls < 1) newValues.totalBalls = 9;

    // 先验证新值是否有效
    const isValid = validationSchema.isValidSync(newValues);
    if (!isValid) {
      console.error('应用模板后的值无效:', newValues);
      // 尝试修复无效的值，但保持名称和地点为空
      // 使用类型断言确保 TypeScript 能够识别这些属性
      const typedValues = newValues as MatchBasicInfo;
      if (!typedValues.date) typedValues.date = new Date().toISOString().split('T')[0];
      if (!typedValues.playerCount || typedValues.playerCount < 2) typedValues.playerCount = 2;
      if (!typedValues.totalBalls || typedValues.totalBalls < 1) typedValues.totalBalls = 9;
    }

    // 只设置表单值，不触发提交，这样就不会跳转到下一页
    formik.setValues(newValues);
  }, [formik]);

  // 使用 useImperativeHandle 暴露方法
  useImperativeHandle(ref, () => ({
    submitForm: () => {
      console.log("[MatchBasicInfoForm] submitForm called via ref");
      formik.submitForm(); // 触发表单提交（包括验证）
    },
    applyFullTemplate: (template: Template) => {
      console.log("[MatchBasicInfoForm] applyFullTemplate called via ref", template.name);

      // 创建模板配置的深拷贝
      const configCopy = JSON.parse(JSON.stringify(template.config));

      // 保留当前的可变信息
      if (configCopy.basicInfo) {
        // 保留当前的比赛名称、日期和地点
        configCopy.basicInfo.name = formik.values.name || '';
        configCopy.basicInfo.date = formik.values.date || new Date().toISOString().split('T')[0];
        configCopy.basicInfo.location = formik.values.location || '';
      }

      // 存储模板数据到 sessionStorage 和全局变量
      try {
        // 先清除之前的模板数据
        sessionStorage.removeItem('current_applying_template');

        // 然后存储新的模板数据
        const templateJson = JSON.stringify(configCopy);
        console.log('[MatchBasicInfoForm] 存储到 sessionStorage 的模板数据:', templateJson);
        sessionStorage.setItem('current_applying_template', templateJson);

        // 在全局变量中也存储一份
        console.log('[MatchBasicInfoForm] 存储到全局变量的模板数据:', configCopy);
        (window as any).currentApplyingTemplate = configCopy;
      } catch (error) {
        console.error('[MatchBasicInfoForm] 存储模板数据时出错:', error);
      }

      // 设置表单值，但保留当前的可变信息
      // 使用类型断言确保 TypeScript 能够识别这些属性
      const basicInfo = template.config.basicInfo as MatchBasicInfo;
      const newValues: MatchBasicInfo = {
        ...basicInfo,
        // 保留空值，不设置默认值
        name: '',
        date: new Date().toISOString().split('T')[0], // 日期需要有值才能通过验证
        location: ''
      };

      // 先验证新值是否有效
      const isValid = validationSchema.isValidSync(newValues);
      if (!isValid) {
        console.error('应用模板后的值无效:', newValues);
        // 尝试修复无效的值，但保持名称和地点为空
        // 使用类型断言确保 TypeScript 能够识别这些属性
        const typedValues = newValues as MatchBasicInfo;
        if (!typedValues.date) typedValues.date = new Date().toISOString().split('T')[0];
        if (!typedValues.playerCount || typedValues.playerCount < 2) typedValues.playerCount = 2;
        if (!typedValues.totalBalls || typedValues.totalBalls < 1) typedValues.totalBalls = 9;
      }

      // 只设置表单值，不触发提交，这样就不会跳转到下一页
      formik.setValues(newValues);

      // 返回完整模板信息，供父组件使用
      return configCopy;
    }
  }), [formik]); // 只依赖 formik 实例

  // 使用 useMemo 优化渲染表单字段
  const renderTextField = useMemo(() => (
    (name: keyof MatchBasicInfo, label: string) => (
      <TextField
        fullWidth
        id={name}
        name={name}
        label={label}
        value={formik.values[name]}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        error={formik.touched[name] && Boolean(formik.errors[name])}
        helperText={formik.touched[name] && typeof formik.errors[name] === 'string' ? formik.errors[name] : undefined} // 确保 helperText 是字符串
        margin="normal"
      />
    )
  ), [formik.values, formik.touched, formik.errors, formik.handleChange, formik.handleBlur]);

  // 使用 useMemo 优化模式相关设置的渲染
  const renderModeSettings = useMemo(() => {
    if (formik.values.mode === 'time') {
      // 安全地获取嵌套错误信息
      const getNestedError = (path: string): string | undefined => {
        const keys = path.split('.');
        let current: unknown = formik.errors;
        for (const key of keys) {
          if (current && typeof current === 'object' && key in current) {
            current = (current as Record<string, unknown>)[key];
          } else {
            return undefined;
          }
        }
        return typeof current === 'string' ? current : undefined;
      };

      const durationError = getNestedError('settings.time.duration');
      const extraTimeError = getNestedError('settings.time.extraTime');

      return (
        <Box>
          <TextField
            fullWidth
            id="settings.time.duration"
            name="settings.time.duration"
            label="比赛时长（分钟）"
            type="number"
            value={formik.values.settings.time?.duration || ''}
            onChange={formik.handleChange}
            error={Boolean(durationError)} // 使用安全获取的错误
            helperText={durationError}       // 使用安全获取的错误
            margin="normal"
          />
          <TextField
            fullWidth
            id="settings.time.extraTime"
            name="settings.time.extraTime"
            label="加时时长（分钟）"
            type="number"
            value={formik.values.settings.time?.extraTime || ''}
            onChange={formik.handleChange}
            error={Boolean(extraTimeError)} // 使用安全获取的错误
            helperText={extraTimeError}       // 使用安全获取的错误
            margin="normal"
          />
        </Box>
      );
    }
    // 定分模式的设置
    if (formik.values.mode === 'score') {
      const getNestedError = (path: string): string | undefined => {
        const keys = path.split('.');
        let current: unknown = formik.errors;
        for (const key of keys) {
          if (current && typeof current === 'object' && key in current) {
            current = (current as Record<string, unknown>)[key];
          } else {
            return undefined;
          }
        }
        return typeof current === 'string' ? current : undefined;
      };

      const targetScoreError = getNestedError('settings.score.targetScore');
      const minFramesError = getNestedError('settings.score.minFrames');

      return (
        <Box>
          <TextField
            fullWidth
            id="settings.score.targetScore"
            name="settings.score.targetScore"
            label="目标分数"
            type="number"
            value={formik.values.settings.score?.targetScore || ''}
            onChange={formik.handleChange}
            error={Boolean(targetScoreError)}
            helperText={targetScoreError}
            margin="normal"
          />
          <TextField
            fullWidth
            id="settings.score.minFrames"
            name="settings.score.minFrames"
            label="最少局数"
            type="number"
            value={formik.values.settings.score?.minFrames || ''}
            onChange={formik.handleChange}
            error={Boolean(minFramesError)}
            helperText={minFramesError}
            margin="normal"
          />
        </Box>
      );
    }

    // 定局模式的设置
    if (formik.values.mode === 'frame') {
      const getNestedError = (path: string): string | undefined => {
        const keys = path.split('.');
        let current: unknown = formik.errors;
        for (const key of keys) {
          if (current && typeof current === 'object' && key in current) {
            current = (current as Record<string, unknown>)[key];
          } else {
            return undefined;
          }
        }
        return typeof current === 'string' ? current : undefined;
      };

      const totalFramesError = getNestedError('settings.frame.totalFrames');
      const winningFramesError = getNestedError('settings.frame.winningFrames');

      return (
        <Box>
          <TextField
            fullWidth
            id="settings.frame.totalFrames"
            name="settings.frame.totalFrames"
            label="总局数"
            type="number"
            value={formik.values.settings.frame?.totalFrames || ''}
            onChange={formik.handleChange}
            error={Boolean(totalFramesError)}
            helperText={totalFramesError}
            margin="normal"
          />
          <TextField
            fullWidth
            id="settings.frame.winningFrames"
            name="settings.frame.winningFrames"
            label="获胜局数"
            type="number"
            value={formik.values.settings.frame?.winningFrames || ''}
            onChange={formik.handleChange}
            error={Boolean(winningFramesError)}
            helperText={winningFramesError}
            margin="normal"
          />
        </Box>
      );
    }

    // 如果没有匹配的模式，返回 null
    return null;
    // 添加 formik.errors.settings 到依赖数组
  }, [formik.values.mode, formik.values.settings, formik.errors.settings, formik.errors, formik.handleChange]);

  return (
    <Box component="div">
      {/* 删除模板确认对话框 */}
      <Dialog
        open={deleteDialogOpen}
        onClose={cancelDeleteTemplate}
        aria-labelledby="delete-template-dialog-title"
      >
        <DialogTitle id="delete-template-dialog-title">删除模板</DialogTitle>
        <DialogContent>
          <DialogContentText>
            您确定要删除模板“{templateToDelete?.name}”吗？此操作无法撤销。
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDeleteTemplate}>取消</Button>
          <Button onClick={confirmDeleteTemplate} color="error" autoFocus>删除</Button>
        </DialogActions>
      </Dialog>
      <Grid container spacing={2}>
        {/* 模板列表 */}
        {templates.length > 0 && (
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>配置模板</Typography>
            <Stack direction="row" spacing={2} sx={{ overflowX: 'auto', pb: 2 }}>
              {templates.map((template) => (
                <Card key={template.id} sx={{ minWidth: 200, maxWidth: 300 }}>
                  <CardContent>
                    <Typography variant="h6" noWrap>{template.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      创建于: {new Date(template.createdAt).toLocaleDateString()}
                    </Typography>
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="body2">
                        选手数: {template.config.basicInfo.playerCount}
                      </Typography>
                      <Typography variant="body2">
                        球数: {template.config.basicInfo.totalBalls}
                      </Typography>
                      <Typography variant="body2">
                        模式: {template.config.basicInfo.mode === 'time' ? '限时赛' :
                               template.config.basicInfo.mode === 'score' ? '定分赛' : '定局赛'}
                      </Typography>
                    </Box>
                  </CardContent>
                  <CardActions>
                    <Button size="small" onClick={() => {
                      // 直接调用 applyFullTemplate 方法
                      if (ref && 'current' in ref && ref.current && ref.current.applyFullTemplate) {
                        ref.current.applyFullTemplate(template);
                      } else {
                        // 如果 ref 不可用，则使用原来的方法
                        applyTemplate(template);
                      }
                    }}>应用模板</Button>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteTemplate(template)}
                      aria-label="删除模板"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </CardActions>
                </Card>
              ))}
            </Stack>
            <Divider sx={{ my: 2 }} />
          </Grid>
        )}
        <Grid item xs={12}>
          {renderTextField('name', '比赛名称')}
          {renderTextField('date', '比赛日期')}
          {renderTextField('location', '比赛地点')}
        </Grid>

        <Grid item xs={12}>
          <FormControl component="fieldset">
            <FormLabel>比赛类型</FormLabel>
            <RadioGroup
              row
              name="matchType"
              value={formik.values.matchType}
              onChange={formik.handleChange}
            >
              <FormControlLabel value="manual" control={<Radio />} label="手动记分" />
              <FormControlLabel value="auto" control={<Radio />} label="自动记分" />
            </RadioGroup>
            {formik.errors.matchType && (
              <FormHelperText error>{formik.errors.matchType}</FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <FormControl component="fieldset">
            <FormLabel>比赛模式</FormLabel>
            <RadioGroup
              row
              name="mode"
              value={formik.values.mode}
              onChange={formik.handleChange}
            >
              <FormControlLabel value="time" control={<Radio />} label="限时" />
              <FormControlLabel value="score" control={<Radio />} label="定分" />
              <FormControlLabel value="frame" control={<Radio />} label="定局" />
            </RadioGroup>
            {formik.errors.mode && (
              <FormHelperText error>{formik.errors.mode}</FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            id="playerCount"
            name="playerCount"
            label="选手数量"
            type="number"
            value={formik.values.playerCount}
            onChange={formik.handleChange}
            error={formik.touched.playerCount && Boolean(formik.errors.playerCount)}
            helperText={formik.touched.playerCount && formik.errors.playerCount}
            margin="normal"
            inputProps={{ min: 2, max: 8 }}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            id="totalBalls"
            name="totalBalls"
            label="总球数"
            type="number"
            value={formik.values.totalBalls}
            onChange={formik.handleChange}
            error={formik.touched.totalBalls && Boolean(formik.errors.totalBalls)}
            helperText={formik.touched.totalBalls && formik.errors.totalBalls}
            margin="normal"
            inputProps={{ min: 1, max: 15 }}
          />
        </Grid>

        <Grid item xs={12}>
          {renderModeSettings}
        </Grid>
      </Grid>
    </Box>
  );
});

MatchBasicInfoForm.displayName = 'MatchBasicInfoForm';

export default MatchBasicInfoForm;
