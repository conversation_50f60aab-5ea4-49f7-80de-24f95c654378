import React, { useState, useEffect, forwardRef, useImperativeHandle, useCallback } from 'react';
import { Box, Typography } from '@mui/material';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, horizontalListSortingStrategy, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { PlayerCard, PlayerBallConfig } from './PlayerCard';

interface PlayerGroupConfigFormProps {
  playerCount: number;
  totalBalls: number;
  onSubmit: (configs: PlayerBallConfig[]) => void;
  initialConfigs?: PlayerBallConfig[] | null;
}

// 定义暴露给父组件的 Ref 类型
export interface PlayerGroupConfigFormRef {
  submitForm: () => void;
}

// 创建可排序的玩家卡片组件
const SortablePlayerCard = ({ config, totalBalls, onConfigChange, id }: {
  config: PlayerBallConfig;
  totalBalls: number;
  onConfigChange: (config: PlayerBallConfig) => void;
  id: string;
}) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.6 : 1,
    marginBottom: 2,
    zIndex: isDragging ? 1 : 0,
    width: '32%', // 设置为约1/3的宽度，确保三个卡片可以在一行显示
    minWidth: '300px' // 设置最小宽度，确保卡片内容显示正常
  };

  return (
    <Box
      ref={setNodeRef}
      style={style}
      data-player-id={config.playerId}
    >
      {/* 只将拖拽句柄添加到卡片的标题区域 */}
      <Box
        {...attributes}
        {...listeners}
        sx={{
          cursor: 'grab',
          padding: '8px',
          backgroundColor: 'rgba(0, 0, 0, 0.03)',
          borderRadius: '4px 4px 0 0',
          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.05)' },
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          mb: 1
        }}
      >
        <Typography variant="subtitle2">拖拽此处移动卡片</Typography>
      </Box>
      <PlayerCard
        config={config}
        totalBalls={totalBalls}
        onConfigChange={onConfigChange}
      />
    </Box>
  );
};

export const PlayerGroupConfigForm = forwardRef<PlayerGroupConfigFormRef, PlayerGroupConfigFormProps>((
  {
    playerCount,
    totalBalls,
    onSubmit,
    initialConfigs,
  },
  ref // 接收 ref
) => {
  console.log('PlayerGroupConfigForm 渲染', { playerCount, totalBalls, initialConfigs });
  const [playerConfigs, setPlayerConfigs] = useState<PlayerBallConfig[]>([]);

  // 使用ref跟踪上一次的totalBalls值
  const prevTotalBallsRef = React.useRef<number>(totalBalls);

  // 设置拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // 增加激活约束，确保只有在拖拽句柄上才能开始拖拽
      activationConstraint: {
        distance: 8, // 需要移动至少8px才会触发拖拽
      }
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 检查totalBalls是否有变化
  useEffect(() => {
    const prevTotalBalls = prevTotalBallsRef.current;
    if (totalBalls !== prevTotalBalls) {
      console.log('totalBalls变化检测到:', { 旧值: prevTotalBalls, 新值: totalBalls });
      prevTotalBallsRef.current = totalBalls;
    }
  }, [totalBalls]);

  // 当组件初始化或playerCount/totalBalls变化时，更新配置
  useEffect(() => {
    console.log('PlayerGroupConfigForm useEffect 触发 - 初始化配置', { initialConfigs, playerCount, totalBalls });

    if (initialConfigs) {
      console.log('initialConfigs中的球数:', initialConfigs[0]?.balls.length, '新的totalBalls:', totalBalls);
      console.log('initialConfigs的完整内容:', JSON.stringify(initialConfigs));

      // 强制记录totalBalls的变化，确保总是重新初始化
      console.log('当前totalBalls:', totalBalls, '上次记录的totalBalls:', prevTotalBallsRef.current);
      const totalBallsChanged = totalBalls !== prevTotalBallsRef.current;
      if (totalBallsChanged) {
        console.log('totalBalls发生变化，强制重新初始化球组配置');
      }
    }

    // 初始化为空数组，确保变量已初始化
    let newConfigs: PlayerBallConfig[] = [];

    // 检查全局变量中是否有模板数据
    const globalTemplate = (window as any).currentApplyingTemplate;
    if (globalTemplate && globalTemplate.playerConfigs) {
      console.log('使用全局变量中的模板数据');
      // 深拷贝球组配置
      newConfigs = JSON.parse(JSON.stringify(globalTemplate.playerConfigs));

      // 重置选手名称
      newConfigs.forEach((player, index) => {
        player.playerName = `选手 ${index + 1}`;
        player.name = `选手 ${index + 1}`;
      });

      console.log('重置选手名称后的配置:', newConfigs);
    }
    // 检查 sessionStorage 中是否有模板数据
    else {
      const templateJson = sessionStorage.getItem('current_applying_template');
      if (templateJson) {
        try {
          const template = JSON.parse(templateJson);
          if (template && template.playerConfigs) {
            console.log('使用 sessionStorage 中的模板数据');
            // 深拷贝球组配置
            newConfigs = JSON.parse(JSON.stringify(template.playerConfigs));

            // 重置选手名称
            newConfigs.forEach((player, index) => {
              player.playerName = `选手 ${index + 1}`;
              player.name = `选手 ${index + 1}`;
            });

            console.log('重置选手名称后的配置:', newConfigs);
          }
        } catch (error) {
          console.error('解析 sessionStorage 中的模板数据时出错:', error);
        }
      }
    }

    // 如果没有找到模板数据，则使用 initialConfigs 或创建默认配置
    if (!newConfigs) {
      if (initialConfigs && initialConfigs.length > 0) {
        console.log('使用初始配置，但需要根据新的totalBalls更新');
        // 直接使用初始配置，不做任何修改
        newConfigs = initialConfigs.map(config => ({
          ...config,
          // 确保球数与 totalBalls 匹配
          balls: config.balls.length === totalBalls ?
            config.balls :
            Array.from({ length: totalBalls }, (_, ballIndex) => {
              const existingBall = config.balls.find(b => b.id === ballIndex + 1);
              return existingBall || {
                id: ballIndex + 1,
                points: 0,
                isEnding: false
              };
            }),
          // 保留原有的 slamConfig
          slamConfig: config.slamConfig || {
            bigSlam: {
              points: 9,
              balls: Array.from({ length: totalBalls }, (_, i) => i + 1)
            },
            smallSlam: {
              points: 6,
              balls: Array.from({ length: totalBalls }, (_, i) => i + 1)
            }
          }
        }));
        console.log('更新后的配置:', JSON.stringify(newConfigs));
      } else {
        console.log('创建默认配置');
        // 初始化选手配置
        newConfigs = Array.from({ length: playerCount }, (_, index) => ({
          playerId: index + 1,
          playerName: `选手 ${index + 1}`,
          name: `选手 ${index + 1}`,
          balls: Array.from({ length: totalBalls }, (_, ballIndex) => ({
            id: ballIndex + 1,
            points: 0,
            isEnding: false
          })),
          slamConfig: {
            bigSlam: {
              points: 9,
              balls: Array.from({ length: totalBalls }, (_, i) => i + 1)
            },
            smallSlam: {
              points: 6,
              balls: Array.from({ length: totalBalls }, (_, i) => i + 1)
            }
          }
        }));
      }
    }

    // 确保 newConfigs 已经被正确初始化
    if (newConfigs) {
      setPlayerConfigs(newConfigs);
      // 初始化时不再调用 onSubmit，避免立即跳到下一步
      console.log('PlayerGroupConfigForm 初始化完成，内部状态设置:', newConfigs);
    } else {
      console.error('PlayerGroupConfigForm 初始化失败，newConfigs 为空');
    }
  }, [playerCount, totalBalls, initialConfigs, onSubmit]);

  // 专门监听totalBalls变化的useEffect
  useEffect(() => {
    console.log('PlayerGroupConfigForm - totalBalls变化检测', totalBalls);

    // 当totalBalls变化时，强制更新所有配置
    if (playerConfigs.length > 0 && prevTotalBallsRef.current !== totalBalls) {
      console.log('检测到totalBalls从', prevTotalBallsRef.current, '变为', totalBalls, '，强制更新所有球配置');

      const newConfigs = playerConfigs.map(config => ({
        ...config,
        balls: Array.from({ length: totalBalls }, (_, ballIndex) => {
          const id = ballIndex + 1;
          const existingBall = config.balls.find(b => b.id === id);
          return existingBall || {
            id,
            points: 0,
            isEnding: false
          };
        }),
        // 保留原有的 slamConfig
        slamConfig: {
          bigSlam: {
            points: config.slamConfig?.bigSlam?.points || 9,
            // 保留原有的 bigSlam.balls
            balls: config.slamConfig?.bigSlam?.balls || Array.from({ length: totalBalls }, (_, i) => i + 1)
          },
          smallSlam: {
            points: config.slamConfig?.smallSlam?.points || 6,
            // 保留原有的 smallSlam.balls
            balls: config.slamConfig?.smallSlam?.balls || Array.from({ length: totalBalls }, (_, i) => i + 1)
          }
        }
      }));

      console.log('强制更新后的配置:', JSON.stringify(newConfigs));
      setPlayerConfigs(newConfigs);
      // 不调用 onSubmit，避免自动跳转
      // onSubmit(newConfigs);

      // 更新记录的totalBalls值
      prevTotalBallsRef.current = totalBalls;
    } else if (playerConfigs.length === 0) {
      // 如果还没有配置，只更新记录的值
      prevTotalBallsRef.current = totalBalls;
    }
  }, [totalBalls, playerConfigs]);

  const handleConfigChange = useCallback((updatedConfig: PlayerBallConfig) => {
    console.log(`%c[PlayerGroupConfigForm] handleConfigChange START - PlayerID: ${updatedConfig.playerId}`, 'color: orange');
    console.log('收到子组件配置变更:', { updatedConfig });
    // 检查是否真的有变化，避免不必要的更新
    const existingConfig = playerConfigs.find(config => config.playerId === updatedConfig.playerId);

    // 如果没有找到对应的配置或者配置没有变化，则不做任何操作
    if (!existingConfig) return;

    // 检查是否有实质性变化
    const hasNameChanged = existingConfig.name !== updatedConfig.name || existingConfig.playerName !== updatedConfig.playerName;
    const hasBallsChanged = JSON.stringify(existingConfig.balls) !== JSON.stringify(updatedConfig.balls);
    const hasSlamConfigChanged = JSON.stringify(existingConfig.slamConfig) !== JSON.stringify(updatedConfig.slamConfig);

    // 如果没有任何变化，则不做任何操作
    if (!hasNameChanged && !hasBallsChanged && !hasSlamConfigChanged) return;

    // 创建新的配置数组，确保引用发生变化
    const newConfigs = playerConfigs.map(config =>
      config.playerId === updatedConfig.playerId ? {...updatedConfig} : {...config}
    );

    // 更新本地状态
    setPlayerConfigs(newConfigs);

    // 注意：我们不再在这里调用 onSubmit。
    // 提交只应通过父组件调用 submitForm 方法来触发。

    console.log(`%c[PlayerGroupConfigForm] handleConfigChange END - PlayerID: ${updatedConfig.playerId}`, 'color: orange');
  }, [playerConfigs]);

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) return;

    if (active.id !== over.id) {
      setPlayerConfigs((items) => {
        const oldIndex = items.findIndex(item => String(item.playerId) === active.id);
        const newIndex = items.findIndex(item => String(item.playerId) === over.id);

        const newItems = arrayMove(items, oldIndex, newIndex);

        // 拖拽结束后不再自动提交，避免自动跳转到下一步
        console.log('拖拽结束后更新选手顺序');
        // 不再调用 onSubmit(newItems);

        return newItems;
      });
    }
  };

  // 使用 useImperativeHandle 暴露 submitForm 方法
  useImperativeHandle(ref, () => ({
    submitForm: () => {
      console.log(`%c[PlayerGroupConfigForm] submitForm START (called via ref)`, 'color: red; font-weight: bold');
      console.log('PlayerGroupConfigForm submitForm called via ref. Submitting configs:', playerConfigs);
      // 检查配置是否有效
      if (playerConfigs && playerConfigs.length > 0) {
        // 可以在这里添加额外的验证逻辑，例如检查球员名字是否为空
        const isEmptyName = playerConfigs.some(p => !p.playerName || p.playerName.trim() === '');
        if (isEmptyName) {
          alert('请为所有选手输入名称。');
          return; // 如果有空名字，则不提交
        }
        onSubmit(playerConfigs); // 调用父组件传入的 onSubmit
      } else {
        console.error('尝试提交无效或空的球组配置');
        alert('球组配置无效，无法提交。');
        console.log(`%c[PlayerGroupConfigForm] submitForm END (Invalid Config)`, 'color: red; font-weight: bold');
      }
      // 注意：submitForm 成功调用 onSubmit 后，自身的日志就结束了
    }
  }), [onSubmit, playerConfigs]); // 依赖 onSubmit 和 playerConfigs

  return (
    <Box component="div" sx={{ width: '100%' }}>
      <Typography variant="h6" sx={{ mb: 2 }}>
        选手球组配置
      </Typography>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={playerConfigs.map(config => String(config.playerId))}
          strategy={horizontalListSortingStrategy}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              flexWrap: 'nowrap', // 修改为nowrap，确保不会换行
              gap: 2,
              justifyContent: 'space-between',
              minHeight: 100,
              mb: 2,
              overflowX: 'auto', // 添加水平滚动条，以防屏幕太小
              paddingBottom: 2 // 为滚动条留出空间
            }}
          >
            {playerConfigs.map((config) => (
              <SortablePlayerCard
                key={config.playerId}
                id={String(config.playerId)}
                config={config}
                totalBalls={totalBalls}
                onConfigChange={handleConfigChange}
              />
            ))}
          </Box>
        </SortableContext>
      </DndContext>
    </Box>
  );
});
