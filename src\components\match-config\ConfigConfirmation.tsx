import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Divider,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Snackbar,
  Alert,
} from '@mui/material';
// 使用 Grid 组件
import Grid from '@mui/material/Grid';
import SaveIcon from '@mui/icons-material/Save';
import { MatchBasicInfo } from './MatchBasicInfoForm';
import { PlayerBallConfig } from './PlayerCard';
import { RuleConfig } from './RuleConfigForm';
import { TemplateService } from '../../services/TemplateService';
import { MatchConfig } from '../../types/MatchConfig';

interface Props {
  basicInfo: MatchBasicInfo;
  playerConfigs: PlayerBallConfig[];
  ruleConfig: RuleConfig;
}

const ConfigConfirmation: React.FC<Props> = ({
  basicInfo,
  playerConfigs,
  ruleConfig,
}) => {
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [templateName, setTemplateName] = useState('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  // 处理保存模板
  const handleSaveTemplate = () => {
    if (!templateName.trim()) {
      setSnackbarMessage('请输入模板名称');
      setSnackbarOpen(true);
      return;
    }

    // 创建配置对象
    const config: MatchConfig = {
      basicInfo,
      playerConfigs,
      ruleConfig
    };

    // 保存模板
    try {
      TemplateService.saveTemplate(templateName, config);
      setSnackbarMessage(`模板 "${templateName}" 保存成功`);
      setSnackbarOpen(true);
      setSaveDialogOpen(false);
      setTemplateName('');
    } catch (error) {
      console.error('保存模板失败', error);
      setSnackbarMessage('保存模板失败');
      setSnackbarOpen(true);
    }
  };

  // 获取比赛类型的显示文本
  const getMatchTypeText = () => {
    switch (basicInfo.matchType) {
      case 'manual':
        return '手动记分';
      case 'auto':
        return '自动记分';
      default:
        return '未设置';
    }
  };

  // 获取比赛模式的显示文本
  const getModeText = () => {
    switch (basicInfo.mode) {
      case 'time':
        return `限时赛：${basicInfo.settings.time?.duration || 0}分钟 (加时${basicInfo.settings.time?.extraTime || 0}分钟)`;
      case 'score':
        return `定分赛：${basicInfo.settings.score?.targetScore || 0}分 (最少${basicInfo.settings.score?.minFrames || 0}局)`;
      case 'frame':
        return `定局赛：${basicInfo.settings.frame?.totalFrames || 0}局 (胜利${basicInfo.settings.frame?.winningFrames || 0}局)`;
      default:
        return '未设置';
    }
  };

  // 获取得分球延迟的显示文本
  const getScoreBallDelayText = () => {
    switch (ruleConfig.advancedOptions.scoreBallDelay) {
      case 1:
        return '延一颗';
      case 2:
        return '延两颗';
      case -1:
        return '一直延';
      default:
        return '未设置';
    }
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
          配置确认
        </Typography>

        {/* 基本信息 */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom color="primary">
            基本信息
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                比赛类型
              </Typography>
              <Typography variant="body1">{getMatchTypeText()}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                比赛模式
              </Typography>
              <Typography variant="body1">{getModeText()}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                参与选手
              </Typography>
              <Typography variant="body1">{basicInfo.playerCount} 人</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                总球数
              </Typography>
              <Typography variant="body1">{basicInfo.totalBalls} 个</Typography>
            </Grid>
          </Grid>
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* 选手球组 */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            选手球组
          </Typography>
          {playerConfigs?.map((config, index) => (
            <Box key={config.playerId} sx={{ mb: 3, p: 2, border: '1px solid #eee', borderRadius: 1 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1, borderBottom: '1px solid #eee', pb: 1 }}>
                选手 {index + 1}: {config.playerName || config.name || `选手 ${index + 1}`}
              </Typography>

              {/* 球组配置 */}
              <Typography variant="subtitle2" sx={{ mt: 1, mb: 0.5 }}>
                球组配置
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                {config.balls.map((ball) => (
                  <Chip
                    key={ball.id}
                    label={`${ball.id}号球 ${ball.points}分`}
                    color={ball.isEnding ? "error" : ball.points > 0 ? "primary" : "default"}
                    size="small"
                  />
                ))}
              </Box>

              {/* 大金球组 */}
              <Typography variant="subtitle2" sx={{ mt: 1, mb: 0.5, color: 'error.main' }}>
                大金球组 (开球轮清台)
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                {config.slamConfig?.bigSlam?.balls?.map((ballId) => (
                  <Chip
                    key={`big-${ballId}`}
                    label={`${ballId}`}
                    color="error"
                    size="small"
                    variant="outlined"
                  />
                )) || (
                  <Typography variant="body2" color="text.secondary">
                    未设置大金球组
                  </Typography>
                )}
              </Box>

              {/* 小金球组 */}
              <Typography variant="subtitle2" sx={{ mt: 1, mb: 0.5, color: 'warning.main' }}>
                小金球组 (非开球轮清台)
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {config.slamConfig?.smallSlam?.balls?.map((ballId) => (
                  <Chip
                    key={`small-${ballId}`}
                    label={`${ballId}`}
                    color="warning"
                    size="small"
                    variant="outlined"
                  />
                )) || (
                  <Typography variant="body2" color="text.secondary">
                    未设置小金球组
                  </Typography>
                )}
              </Box>
            </Box>
          ))}
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* 规则配置 */}
        <Box>
          <Typography variant="h6" gutterBottom color="primary">
            规则配置
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                遮挡球让球
              </Typography>
              <Typography variant="body1">
                {ruleConfig.foulRules.allowPushAfterBlock ? '允许' : '不允许'}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                得分球延迟
              </Typography>
              <Typography variant="body1">{getScoreBallDelayText()}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                清台模式
              </Typography>
              <Typography variant="body1">
                {ruleConfig.advancedOptions.clearTableMode === 'allGold' ? '全球金' : '1-9金'}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                犯规分值
              </Typography>
              <Typography variant="body1">{ruleConfig.foulRules.foulPenaltyPoints} 分</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                犯规罚分处理
              </Typography>
              <Typography variant="body1">
                {ruleConfig.foulRules.foulPointsHandler === 'nextPlayer' ? '加到下家分数' :
                 ruleConfig.foulRules.foulPointsHandler === 'opponent' ? '平分给对手' :
                 ruleConfig.foulRules.foulPointsHandler === 'pool' ? '累计到犯规池' : '仅罚分'}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                开球轮分数
              </Typography>
              <Typography variant="body1">
                {ruleConfig.breakRules.breakShotPoints === 'double' ? '分数加倍' : '正常计分'}
              </Typography>
            </Grid>
          </Grid>
        </Box>
      </CardContent>
      {/* 保存模板按钮 */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 2 }}>
        <Button
          variant="outlined"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={() => setSaveDialogOpen(true)}
        >
          保存为模板
        </Button>
      </Box>

      {/* 保存模板对话框 */}
      <Dialog open={saveDialogOpen} onClose={() => setSaveDialogOpen(false)}>
        <DialogTitle>保存为模板</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="模板名称"
            type="text"
            fullWidth
            value={templateName}
            onChange={(e) => setTemplateName(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveDialogOpen(false)}>取消</Button>
          <Button onClick={handleSaveTemplate} color="primary">保存</Button>
        </DialogActions>
      </Dialog>

      {/* 提示消息 */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
      >
        <Alert onClose={() => setSnackbarOpen(false)} severity="success" sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Card>
  );
};

export default ConfigConfirmation;