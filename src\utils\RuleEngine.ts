import { RuleConfig } from '../components/match-config/RuleConfigForm';
import { PlayerBallConfig } from '../components/match-config/PlayerCard';

export interface GameState {
  currentPlayerId: number;
  nextPlayerId: number;
  previousPlayerId: number;
  remainingBalls: number[];
  currentMinBall: number;
  isObstructed: boolean;
  canLetShot: boolean;
  isBreakShot: boolean;
  playerCount: number;
  originalPlayerOrder?: number[]; // 让球前的原始顺序
  foulPool?: number;               // 犯规分累计池
  isFrameOver?: boolean;           // 局结束标志
}

export interface TurnResult {
  pocketedBalls: number[];
  hasFoul: boolean;
  scores: Record<number, number>;
  specialEvent?: string;
  nextState: GameState;
}

export class RuleEngine {
  private ruleConfig: RuleConfig;
  private playerConfigs: PlayerBallConfig[];
  private playerCount: number;

  constructor(ruleConfig: RuleConfig, playerConfigs: PlayerBallConfig[], playerCount: number) {
    this.ruleConfig = ruleConfig;
    this.playerConfigs = playerConfigs;
    this.playerCount = playerCount; // playerConfigs.length 应该等于 playerCount
  }

  // 获取球的分值
  private getBallPoints(ballId: number, playerId: number): number {
    // 获取选手的配置
    const playerConfig = this.playerConfigs.find(p => p.playerId === playerId);
    if (!playerConfig) return 0;

    // 获取球的分值
    const ballConfig = playerConfig.balls.find(b => b.id === ballId);
    return ballConfig?.points || 0;
  }

  // 处理让球
  handleLetShot(state: GameState): GameState {
    // 保存原始玩家顺序
    const originalPlayerOrder = [state.currentPlayerId, state.nextPlayerId, state.previousPlayerId];

    // 创建新状态
    const newState: GameState = {
      ...state,
      // 交换当前玩家和下一个玩家
      currentPlayerId: state.nextPlayerId,
      nextPlayerId: state.currentPlayerId,
      // 保存原始顺序以便后续恢复
      originalPlayerOrder: originalPlayerOrder,
      // 重置开球标志
      isBreakShot: false,
      // 重置让球标志
      canLetShot: false
    };

    console.log(`让球处理: 从选手 ${state.currentPlayerId} 到选手 ${newState.currentPlayerId}`);
    return newState;
  }

  // 计算得分和更新状态
  evaluateTurn(
    state: GameState,
    pocketedBalls: number[],
    hasFoul: boolean
  ): TurnResult {
    const scores: Record<number, number> = {};
    let specialEvent: string | undefined;
    let foulPool = state.foulPool || 0;

    // 检查清台情况

    // 大金判断：开球轮清台
    if (state.isBreakShot && pocketedBalls.length > 0 &&
        state.remainingBalls.length === pocketedBalls.length &&
        this.ruleConfig.specialWinConditions.slamPoints) {

      if (!hasFoul) {
        // 大金：开球轮清台
        console.log('识别到大金事件: 开球轮清台');
        scores[state.currentPlayerId] = this.ruleConfig.specialWinConditions.slamPoints * 2;
        this.playerConfigs.forEach(p => {
          if (p.playerId !== state.currentPlayerId) {
            scores[p.playerId] = -(this.ruleConfig.specialWinConditions.slamPoints || 0);
          }
        });
        specialEvent = '大金';
      } else {
        // 黑大金：开球轮犯规清台
        console.log('识别到黑大金事件: 开球轮犯规清台');
        scores[state.currentPlayerId] = -(this.ruleConfig.specialWinConditions.slamPoints * 2);
        this.playerConfigs.forEach(p => {
          if (p.playerId !== state.currentPlayerId) {
            scores[p.playerId] = (this.ruleConfig.specialWinConditions.slamPoints || 0);
          }
        });
        specialEvent = '黑大金';
      }

      // 设置局结束标志
      const nextState = this.calculateNextState(state, pocketedBalls, hasFoul);
      nextState.isFrameOver = true;

      // 大金/黑大金结束，直接返回结果
      return {
        pocketedBalls,
        hasFoul,
        scores,
        specialEvent,
        nextState
      };
    }
    // 小金判断：非开球轮，需要检查小金球组
    else if (!state.isBreakShot && !hasFoul && pocketedBalls.length > 0) {
      // 获取当前选手的小金配置
      const currentPlayerConfig = this.playerConfigs.find(p => p.playerId === state.currentPlayerId);
      const smallSlamBalls = currentPlayerConfig?.slamConfig?.smallSlam?.balls || [];
      const smallSlamPoints = currentPlayerConfig?.slamConfig?.smallSlam?.points ||
                           this.ruleConfig.specialWinConditions.slamPoints || 0;

      // 检查是否满足小金条件
      // 1. 小金球组必须有球
      // 2. 打进的球必须包含小金球组中的所有球
      // 3. 打进的球必须全部在小金球组中
      if (smallSlamBalls.length > 0 &&
          smallSlamBalls.every(ballId => pocketedBalls.includes(ballId)) &&
          pocketedBalls.every(ballId => smallSlamBalls.includes(ballId))) {

        console.log('识别到小金事件: 非开球轮清台小金球组');
        console.log('小金球组:', smallSlamBalls, '打进的球:', pocketedBalls);

        if (smallSlamPoints > 0) {
          // 正常小金
          scores[state.currentPlayerId] = (scores[state.currentPlayerId] || 0) + smallSlamPoints;

          // 前序选手扣分
          if (state.previousPlayerId !== undefined && state.previousPlayerId !== null) {
            scores[state.previousPlayerId] = (scores[state.previousPlayerId] || 0) - smallSlamPoints;
          }

          specialEvent = '小金';

          // 设置局结束标志
          const nextState = this.calculateNextState(state, pocketedBalls, hasFoul);
          nextState.isFrameOver = true;

          // 小金结束，直接返回结果
          return {
            pocketedBalls,
            hasFoul,
            scores,
            specialEvent,
            nextState
          };
        }
      }
    }
    // 黑小金判断：非开球轮，犯规，需要检查小金球组
    else if (!state.isBreakShot && hasFoul && pocketedBalls.length > 0) {
      // 获取当前选手的小金配置
      const currentPlayerConfig = this.playerConfigs.find(p => p.playerId === state.currentPlayerId);
      const smallSlamBalls = currentPlayerConfig?.slamConfig?.smallSlam?.balls || [];
      const smallSlamPoints = currentPlayerConfig?.slamConfig?.smallSlam?.points ||
                           this.ruleConfig.specialWinConditions.slamPoints || 0;

      // 检查是否满足黑小金条件
      if (smallSlamBalls.length > 0 &&
          smallSlamBalls.every(ballId => pocketedBalls.includes(ballId)) &&
          pocketedBalls.every(ballId => smallSlamBalls.includes(ballId))) {

        console.log('识别到黑小金事件: 非开球轮犯规清台小金球组');
        console.log('小金球组:', smallSlamBalls, '打进的球:', pocketedBalls);

        if (smallSlamPoints > 0) {
          // 黑小金
          scores[state.currentPlayerId] = (scores[state.currentPlayerId] || 0) - smallSlamPoints;

          // 前序选手得分
          if (state.previousPlayerId !== undefined && state.previousPlayerId !== null) {
            scores[state.previousPlayerId] = (scores[state.previousPlayerId] || 0) + smallSlamPoints;
          }

          specialEvent = '黑小金';

          // 设置局结束标志
          const nextState = this.calculateNextState(state, pocketedBalls, hasFoul);
          nextState.isFrameOver = true;

          // 黑小金结束，直接返回结果
          return {
            pocketedBalls,
            hasFoul,
            scores,
            specialEvent,
            nextState
          };
        }
      }
    }

    // 检查9号球特殊情况
    if (pocketedBalls.includes(9)) {
      if (state.isBreakShot && this.ruleConfig.specialWinConditions.goldenBreakNineBall) {
        // 黄金9: 开球进9号球
        if (hasFoul && this.ruleConfig.specialWinConditions.blackNineBallLoss) {
          // 黑9: 犯规
          specialEvent = '黑9';

          // 获取9号球的分值
          const ball9Points = this.getBallPoints(9, state.currentPlayerId);

          // 当前选手扣除分数
          scores[state.currentPlayerId] = -ball9Points;

          // 后序选手获得分数
          scores[state.nextPlayerId] = ball9Points;

          // 设置局结束标志
          const nextState = this.calculateNextState(state, pocketedBalls, hasFoul);
          nextState.isFrameOver = true;

          return {
            pocketedBalls,
            hasFoul,
            scores,
            specialEvent,
            nextState
          };
        } else if (!hasFoul) {
          // 黄金9: 未犯规
          specialEvent = '黄金9';

          // 获取9号球的分值
          const ball9Points = this.getBallPoints(9, state.currentPlayerId);

          // 当前选手获得双倍分数
          scores[state.currentPlayerId] = ball9Points * 2;

          // 其他所有选手扣除分数
          this.playerConfigs.forEach(p => {
            if (p.playerId !== state.currentPlayerId) {
              scores[p.playerId] = -ball9Points;
            }
          });

          // 设置局结束标志
          const nextState = this.calculateNextState(state, pocketedBalls, hasFoul);
          nextState.isFrameOver = true;

          return {
            pocketedBalls,
            hasFoul,
            scores,
            specialEvent,
            nextState
          };
        }
      } else if (!state.isBreakShot) {
        // 正常打进9号球 (非开球轮)
        // 在这里处理非开球轮打进9号球的情况
        if (hasFoul) {
          // 犯规失败：当前选手在将 9 号球打进袋并犯规，其前序选手获胜，当前选手为败者
          specialEvent = '赢得本局';

          // 获取9号球的分值
          const ball9Points = this.getBallPoints(9, state.currentPlayerId);

          // 当前选手扣除分数
          scores[state.currentPlayerId] = -ball9Points;

          // 前序选手获得分数
          if (state.previousPlayerId !== undefined && state.previousPlayerId !== null) {
            scores[state.previousPlayerId] = ball9Points;
          }

          // 设置局结束标志
          const nextState = this.calculateNextState(state, pocketedBalls, hasFoul);
          nextState.isFrameOver = true;

          return {
            pocketedBalls,
            hasFoul,
            scores,
            specialEvent,
            nextState
          };
        } else {
          // 正常获胜：当前选手在合法击球中将 9 号球打进袋并未犯规，直接获胜。其前序选手为败者
          specialEvent = '赢得本局';

          // 计算所有打进球的分值
          let totalPoints = 0;

          // 获取当前选手的配置
          const currentPlayerConfig = this.playerConfigs.find(p => p.playerId === state.currentPlayerId);
          if (currentPlayerConfig) {
            // 遍历所有打进的球
            for (const ballId of pocketedBalls) {
              // 获取球的分值
              const ballConfig = currentPlayerConfig.balls.find(b => b.id === ballId);
              if (ballConfig) {
                totalPoints += ballConfig.points || 0;
              }
            }
          }

          // 当前选手得分
          scores[state.currentPlayerId] = totalPoints;

          // 前序选手扣分
          if (state.previousPlayerId !== undefined && state.previousPlayerId !== null) {
            scores[state.previousPlayerId] = -totalPoints;
          }

          // 设置局结束标志
          const nextState = this.calculateNextState(state, pocketedBalls, hasFoul);
          nextState.isFrameOver = true;

          return {
            pocketedBalls,
            hasFoul,
            scores,
            specialEvent,
            nextState
          };
        }
      }
    }

    // 初始化得分记录 (确保所有玩家都有记录)
    // 注意：不要重置分数，只初始化空值
    for (let i = 0; i < this.playerCount; i++) {
      if (!scores[i]) scores[i] = 0;
    }

    // 获取当前选手的配置（主要是目标球和分值）
    const currentPlayerConfig = this.playerConfigs.find(p => p.playerId === state.currentPlayerId);
    if (!currentPlayerConfig) {
        console.error(`Cannot find player config for ID: ${state.currentPlayerId}`);
        // 返回错误或默认状态？取决于错误处理策略
        return { pocketedBalls, hasFoul, scores, nextState: state };
    }

    // 计算本轮进球得分，只计算分数球
    let turnBallPoints = 0;
    const currentTurnPocketedBalls = [...pocketedBalls]; // 复制一份用于计算

    // 过滤出有分值的球
    const scoringBalls = currentTurnPocketedBalls.filter(ballId => {
      const ballConfig = currentPlayerConfig.balls.find(b => b.id === ballId);
      return ballConfig && ballConfig.points > 0;
    });

    // 只计算有分值的球的分数
    scoringBalls.forEach(ballId => {
      const ballConfig = currentPlayerConfig.balls.find(b => b.id === ballId);
      if (ballConfig && ballConfig.points > 0) {
        turnBallPoints += ballConfig.points;
      }
    });

    // 输出调试信息
    console.log('本轮进球情况:', {
      allBalls: currentTurnPocketedBalls,
      scoringBalls,
      turnBallPoints
    });

    // 应用开球轮特殊计分 (黄金/黑金) - 仅当非大金情况下执行
    if (state.isBreakShot && this.playerCount >= 3 && this.ruleConfig.breakRules.breakShotPoints === 'double' && turnBallPoints > 0 && !specialEvent) {
      if (hasFoul) {
        // 黑金开球
        console.log('识别到黑金开球事件');
        specialEvent = '黑金开球';

        // 检查是否是黑金5事件（开球轮犯规进5号球）
        const isBlack5 = pocketedBalls.includes(5);
        console.log('检查黑金5事件:', { pocketedBalls, isBlack5 });

        // 计算分数 - 黑金开球/黑金5事件
        // 当前选手扣除 "进球总分值 * 2"
        scores[state.currentPlayerId] = (scores[state.currentPlayerId] || 0) - turnBallPoints * 2;

        // 其余每位选手获得 "进球总分值"
        this.playerConfigs.forEach(p => {
          if (p.playerId !== state.currentPlayerId) {
            scores[p.playerId] = (scores[p.playerId] || 0) + turnBallPoints;
          }
        });

        // 输出详细的分数计算日志
        console.log('黑金开球/黑金5分数计算:');
        console.log('当前选手ID:', state.currentPlayerId, '扣分:', -turnBallPoints * 2);
        console.log('其他选手各得分:', turnBallPoints);
        console.log('分数计算前状态:', {...scores});
        console.log('最终分数:', scores);

        // 如果是黑金5事件，不结束局
        if (isBlack5) {
          console.log('识别到黑金5事件，只计分不结束局');
          specialEvent = '黑金5';

          // 计算下一个状态，但不结束局
          const nextState = this.calculateNextState(state, pocketedBalls, hasFoul);

          return {
            pocketedBalls,
            hasFoul,
            scores,
            specialEvent,
            nextState
          };
        } else {
          // 其他黑金开球事件，结束局
          const nextState = this.calculateNextState(state, pocketedBalls, hasFoul);
          nextState.isFrameOver = true;

          // 黑金开球结束，直接返回结果
          return {
            pocketedBalls,
            hasFoul,
            scores,
            specialEvent,
            nextState
          };
        }
      } else {
        // 黄金开球
        console.log('识别到黄金开球事件');
        specialEvent = '黄金开球';
        scores[state.currentPlayerId] = (scores[state.currentPlayerId] || 0) + turnBallPoints * 2;
        this.playerConfigs.forEach(p => {
          if (p.playerId !== state.currentPlayerId) {
            scores[p.playerId] = (scores[p.playerId] || 0) - turnBallPoints;
          }
        });

        // 检查是否是黄金5事件（开球轮合法进5号球）
        const isGolden5 = pocketedBalls.includes(5);
        console.log('检查黄金5事件:', { pocketedBalls, isGolden5 });

        // 如果是黄金5事件，不结束局
        if (isGolden5) {
          console.log('识别到黄金5事件，只计分不结束局');
          specialEvent = '黄金5';

          // 计算下一个状态，但不结束局
          const nextState = this.calculateNextState(state, pocketedBalls, hasFoul);

          return {
            pocketedBalls,
            hasFoul,
            scores,
            specialEvent,
            nextState
          };
        } else {
          // 其他黄金开球事件，结束局
          const nextState = this.calculateNextState(state, pocketedBalls, hasFoul);
          nextState.isFrameOver = true;

          // 黄金开球结束，直接返回结果
          return {
            pocketedBalls,
            hasFoul,
            scores,
            specialEvent,
            nextState
          };
        }
      }
      // 黄金/黑金开球得分覆盖普通得分逻辑
      turnBallPoints = 0; // 重置，避免下方重复计算
    }

    // 计算有分值的球的总分
    let scoringBallPoints = 0;

    // 遍历所有进的球，找出有分值的球
    pocketedBalls.forEach(ballId => {
      const ballConfig = currentPlayerConfig.balls.find(b => b.id === ballId);
      if (ballConfig && ballConfig.points > 0) {
        scoringBallPoints += ballConfig.points;
      }
    });

    // 检查是否是普通黑5事件（非开球轮犯规进5号球）
    const isNormalBlack5 = !state.isBreakShot && hasFoul && pocketedBalls.includes(5);

    // 如果是普通黑5事件，设置特殊事件
    if (isNormalBlack5) {
      specialEvent = '普通黑5';
      console.log('规则引擎识别到普通黑5事件');
      console.log('普通黑5得分计算: 当前选手扣分，前序选手得分');
    }

    // 只有当有分值的球时才计算得失分
    if (scoringBallPoints > 0) {
      // 处理普通进球得分 (非开球轮，或开球轮非黄金/黑金情况)
      // 注意：必须有进球才计算分数变化
      if (!hasFoul) {
        // 当前选手合法进球，得分
        scores[state.currentPlayerId] = (scores[state.currentPlayerId] || 0) + scoringBallPoints;

        // 前序选手扣分（规则：当前选手进球时，前序选手扣除相同分值）
        if (state.previousPlayerId !== undefined && state.previousPlayerId !== null) {
          scores[state.previousPlayerId] = (scores[state.previousPlayerId] || 0) - scoringBallPoints;
        }
      } else {
        // 当前选手犯规进球（黑n），扣分
        scores[state.currentPlayerId] = (scores[state.currentPlayerId] || 0) - scoringBallPoints;

        // 前序选手得分
        if (state.previousPlayerId !== undefined && state.previousPlayerId !== null) {
          scores[state.previousPlayerId] = (scores[state.previousPlayerId] || 0) + scoringBallPoints;
        }
      }
    }

    // 处理普通犯规罚分 (如果发生犯规且非特殊事件)
    // 只在没有分数球进球的犯规中才计算犯规罚分
    // 注意：当有分数球进球并犯规时，不再额外计算犯规罚分
    // 特殊事件包括黑金5、黄金5、黑金开球、黄金开球等
    // 黑金5事件已经在前面计算了分数，这里不再重复计算犯规罚分
    console.log('检查是否需要计算普通犯规罚分:', { hasFoul, specialEvent, scoringBallPoints, pocketedBalls });
    console.log('当前分数状态:', scores);
    if (hasFoul && !specialEvent && scoringBallPoints === 0 && !pocketedBalls.includes(5)) {
      console.log('处理普通犯规罚分（无分数球进球）');
      // 确保 foulRules 和 foulPenaltyPoints 存在
      const foulPenalty = this.ruleConfig.foulRules?.foulPenaltyPoints || 0;
      const foulHandler = this.ruleConfig.foulRules?.foulPointsHandler;

      if (foulPenalty > 0) {
        // 罚当前选手的分
        scores[state.currentPlayerId] = (scores[state.currentPlayerId] || 0) - foulPenalty;
        console.log(`普通犯规：当前选手 ${state.currentPlayerId} 扣 ${foulPenalty} 分`);

        // 记录犯规信息，便于在日志中显示
        specialEvent = '普通犯规';

        // 根据 foulPointsHandler 分配罚分
        switch (foulHandler) {
          case 'opponent': {
            const opponents = this.playerConfigs.filter(p => p.playerId !== state.currentPlayerId);
            if (opponents.length > 0) {
              // 平分给所有对手，使用向下取整确保不会出现小数
              const pointsPerOpponent = Math.floor(foulPenalty / opponents.length);

              // 计算总分配分数
              let totalDistributed = pointsPerOpponent * opponents.length;

              // 分配给每个对手
              opponents.forEach(opp => {
                scores[opp.playerId] = (scores[opp.playerId] || 0) + pointsPerOpponent;
                console.log(`普通犯规：对手 ${opp.playerId} 得 ${pointsPerOpponent} 分`);
              });

              // 如果有剩余分数，分配给第一个对手
              const remainingPoints = foulPenalty - totalDistributed;
              if (remainingPoints > 0 && opponents.length > 0) {
                scores[opponents[0].playerId] = (scores[opponents[0].playerId] || 0) + remainingPoints;
                console.log(`普通犯规：对手 ${opponents[0].playerId} 额外得 ${remainingPoints} 分`);
              }
            }
            break;
          }
          case 'pool': {
            foulPool += foulPenalty; // 累加到本轮的 foulPool 变量
            console.log(`普通犯规：犯规池增加 ${foulPenalty} 分`);
            break;
          }
          case 'nextPlayer': {
             // 确保 nextPlayerId 存在
             if (state.nextPlayerId !== undefined && state.nextPlayerId !== null) {
                 scores[state.nextPlayerId] = (scores[state.nextPlayerId] || 0) + foulPenalty;
                 console.log(`普通犯规：后序选手 ${state.nextPlayerId} 得 ${foulPenalty} 分`);
             }
            break;
          }
          // default: 仅罚分，不分配 (已完成)
        }
      }
       // 可选：如果规则规定犯规会使本轮进球无效，可以在此将 turnBallPoints 清零或调整 scores
    } else if (hasFoul && scoringBallPoints > 0) {
      console.log('已有分数球进球并犯规（黑n），不重复计算犯规罚分');
      console.log('当前分数球分值:', scoringBallPoints);
      console.log('当前特殊事件:', specialEvent || '无');
      console.log('当前分数状态:', scores);

      // 检查是否是黑金5事件
      if (specialEvent === '黑金5' || (state.isBreakShot && pocketedBalls.includes(5))) {
        console.log('黑金5事件犯规处理：开球轮犯规进5号球，已在特殊事件中计算分数，不再额外计算犯规罚分');
        console.log('黑金5事件分数详情:');
        console.log('当前选手ID:', state.currentPlayerId, '扣分:', scores[state.currentPlayerId]);
        console.log('当前分数状态:', {...scores});
        this.playerConfigs.forEach(p => {
          if (p.playerId !== state.currentPlayerId) {
            console.log('选手ID:', p.playerId, '得分:', scores[p.playerId]);
          }
        });
      }
    }

    // 计算下一个状态
    // 注意: calculateNextState 可能也需要访问更新后的 scores 或 foulPool，
    // 但当前假设它主要确定下一玩家和剩余球。
    const nextState = this.calculateNextState(state, pocketedBalls, hasFoul);
    // 将更新后的 foulPool 传递到下一个状态
    nextState.foulPool = foulPool;

    // 返回结果
    return {
      pocketedBalls,
      hasFoul,
      scores, // 包含普通得分和犯规罚分调整
      specialEvent,
      nextState // 包含更新后的 foulPool 和下一位玩家等信息
    };
  } // End of evaluateTurn method

  // 分配犯规池分数
  distributeFoulPool(state: GameState, targetPlayerId: number): TurnResult {
    const scores: Record<number, number> = {};
    for (let i = 0; i < this.playerCount; i++) {
      scores[i] = 0;
    }

    if (state.foulPool && state.foulPool > 0) {
      scores[targetPlayerId] = state.foulPool;
      const nextState = { ...state, foulPool: 0 };
      return {
        pocketedBalls: [],
        hasFoul: false,
        scores,
        specialEvent: '犯规池分配',
        nextState
      };
    }
    return {
      pocketedBalls: [],
      hasFoul: false,
      scores,
      nextState: state
    };
  }

  // 检查比赛是否结束
  checkMatchEnd(
    currentScores: Record<number, number>,
    currentFrameNumber: number, // 传入当前局数
    framesWon: Record<number, number> // 传入每个选手赢的局数
  ): { isEnd: boolean; winnerId?: number; reason?: string } {
    // 按局数限制判断
    if (this.ruleConfig.endCondition === 'frames') {
      const totalFrames = this.ruleConfig.totalFrames;
      const winningFrames = this.ruleConfig.winningFrames;

      // 检查是否有人达到获胜局数
      if (winningFrames) {
        for (const [id, wins] of Object.entries(framesWon)) {
          if (wins >= winningFrames) {
            return {
              isEnd: true,
              winnerId: Number(id),
              reason: `已达到获胜局数 (${winningFrames})`
            };
          }
        }
      }

      // 检查是否达到总局数
      if (totalFrames && currentFrameNumber >= totalFrames) {
        // 找出胜局数最多的选手
        let maxWins = 0;
        let winnerId: number | undefined;
        let isTie = false;

        for (const [id, wins] of Object.entries(framesWon)) {
          if (wins > maxWins) {
            maxWins = wins;
            winnerId = Number(id);
            isTie = false;
          } else if (wins === maxWins) {
            isTie = true;
          }
        }

        // 如果是平局且有决胜规则
        if (isTie && this.ruleConfig.tieBreaker) {
          if (this.ruleConfig.tieBreaker === 'highestScore') {
            // 按最高分决胜
            let maxScore = -Infinity;
            winnerId = undefined;

            for (const [id, score] of Object.entries(currentScores)) {
              if (score > maxScore) {
                maxScore = score;
                winnerId = Number(id);
              }
            }

            return {
              isEnd: true,
              winnerId,
              reason: '已达到总局数，按最高分决胜'
            };
          }
          // 其他决胜规则...
        }

        return {
          isEnd: true,
          winnerId: isTie ? undefined : winnerId,
          reason: isTie ? '已达到总局数，平局' : '已达到总局数'
        };
      }
    }

    // 按分数判断
    if (this.ruleConfig.endCondition === 'score') {
      const targetScore = this.ruleConfig.targetScore;
      if (targetScore) {
        for (const [id, score] of Object.entries(currentScores)) {
          if (score >= targetScore) {
            return {
              isEnd: true,
              winnerId: Number(id),
              reason: `已达到目标分数 (${targetScore})`
            };
          }
        }
      }
    }

    // 按时间判断 (暂不实现，需要计时器集成)
    // if (this.ruleConfig.endCondition === 'time') { ... }

    return { isEnd: false };
  }

  private calculateNextState(
    state: GameState,
    pocketedBalls: number[],
    hasFoul: boolean
  ): GameState {
    // 创建下一个状态的基础，复制当前状态
    const nextState: GameState = {
      ...state,
      // 重置与回合相关的状态
      isBreakShot: false, // 通常下一回合不再是开球
      isObstructed: false, // 假设默认不被阻碍，除非特殊规则设置
      canLetShot: false,   // 默认不能让球
    };

    // 确定下一位玩家
    // 如果设置了局结束标志，则不需要轮转选手
    if (nextState.isFrameOver) {
      return nextState;
    }

    // 如果犯规，切换到下一位选手
    if (hasFoul) {
      // 在3人及以上的情况下，按照顺序轮转
      if (this.playerCount >= 3) {
        // 保存当前的选手顺序
        const currentId = state.currentPlayerId;
        const nextId = state.nextPlayerId;
        const previousId = state.previousPlayerId;

        // 轮转选手
        nextState.currentPlayerId = nextId;
        nextState.nextPlayerId = previousId;
        nextState.previousPlayerId = currentId;
      } else {
        // 2人情况下，直接交换当前选手和下一位选手
        nextState.currentPlayerId = state.nextPlayerId;
        nextState.nextPlayerId = state.currentPlayerId;
      }
      return nextState;
    }

    // 正常情况下，犯规或没有进球时需要轮转选手
    const needsPlayerChange = hasFoul || pocketedBalls.length === 0;

    if (needsPlayerChange) {
      if (state.originalPlayerOrder) {
        // 如果是让球状态，恢复原始顺序
        [
          nextState.currentPlayerId,
          nextState.nextPlayerId,
          nextState.previousPlayerId
        ] = state.originalPlayerOrder;

        // 然后正常轮转
        const temp = nextState.currentPlayerId;
        nextState.currentPlayerId = nextState.nextPlayerId;
        nextState.previousPlayerId = temp;
        nextState.nextPlayerId = (nextState.currentPlayerId + 1) % this.playerCount;

        // 清除原始顺序记录
        nextState.originalPlayerOrder = undefined;
      } else {
        // 正常轮转到下一个选手 - 严格按照PRD中的轮换规则
        // 1. 上一轮的后序选手成为当前轮的当前选手
        // 2. 上一轮的当前选手成为当前轮的前序选手
        // 3. 上一轮的前序选手成为当前轮的后序选手

        // 保存原始角色以便轮换
        const tempCurrentId = state.currentPlayerId;
        const tempPreviousId = state.previousPlayerId;
        const tempNextId = state.nextPlayerId;

        // 执行轮换
        nextState.currentPlayerId = tempNextId;      // 原后序变为当前
        nextState.previousPlayerId = tempCurrentId;  // 原当前变为前序
        nextState.nextPlayerId = tempPreviousId;     // 原前序变为后序

        // 移除控制台日志，避免重复记录
        // 不再从 playerConfigs 中计算，因为玩家角色不一定按配置顺序排列
      }
      nextState.isBreakShot = false; // 只有第一杆是开球
    } else {
      nextState.isBreakShot = false; // 不换人时也设置为false
    }

    // 检查最小球是否被遮挡
    // 在实际应用中，这应该由用户输入或系统检测决定
    // 为了测试让球功能，我们暂时设置为 true
    nextState.isObstructed = true;

    // 根据规则配置和当前状态判断是否可以让球
    // 只有当规则允许让球且当前球被遮挡时才能让球
    nextState.canLetShot = this.ruleConfig.foulRules?.allowPushAfterBlock && nextState.isObstructed;

    // 更新剩余球
    nextState.remainingBalls = state.remainingBalls.filter(
      (ballId) => !pocketedBalls.includes(ballId)
    );

    // 更新当前最小球
    nextState.currentMinBall = Math.min(...nextState.remainingBalls);

    return nextState;
  }

}
