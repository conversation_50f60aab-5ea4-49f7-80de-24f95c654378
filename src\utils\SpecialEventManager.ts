/**
 * 特殊事件管理工具类
 * 统一处理特殊事件的检测、显示和记录逻辑
 */

export interface SpecialEventInfo {
  type: 'blackGolden5' | 'golden5' | 'normalBlack5' | 'blackGolden9' | 'golden9' | null;
  triggerId: number | null;
  ballId?: number;
  description?: string;
}

export class SpecialEventManager {
  /**
   * 从日志条目中检测特殊事件
   * @param logEntries 日志条目数组
   * @param players 选手信息
   * @returns 特殊事件信息
   */
  static detectSpecialEventFromLogs(
    logEntries: string[],
    players: Array<{id: number; name: string}>
  ): SpecialEventInfo {
    console.log('检测特殊事件，日志条目:', logEntries);
    console.log('选手信息:', players);
    // 检测黑金5事件
    const blackGolden5Entry = logEntries.find(entry =>
      entry.includes('特殊事件: 黑金5')
    );

    if (blackGolden5Entry) {
      // 对于黑金5事件，我们需要从进球日志中找到触发者
      // 查找包含"进了 5 号球"且包含"犯规"的日志条目
      const potEntry = logEntries.find(entry =>
        entry.includes('进了 5 号球') && entry.includes('犯规')
      );

      if (potEntry) {
        const triggerId = this.extractPlayerIdFromLog(potEntry, players, '进了');
        if (triggerId !== null) {
          return {
            type: 'blackGolden5',
            triggerId,
            ballId: 5,
            description: '开球轮犯规进5号球'
          };
        }
      }

      // 如果没有找到进球日志，尝试从扣除日志中提取
      const triggerId = this.extractPlayerIdFromLog(blackGolden5Entry, players, '扣除');
      if (triggerId !== null) {
        return {
          type: 'blackGolden5',
          triggerId,
          ballId: 5,
          description: '开球轮犯规进5号球'
        };
      }
    }

    // 检测黄金5事件
    const golden5Entry = logEntries.find(entry =>
      entry.includes('特殊事件: 黄金5')
    );

    console.log('黄金5事件日志:', golden5Entry);

    if (golden5Entry) {
      // 对于黄金5事件，我们需要从进球日志中找到触发者
      // 查找包含"进了 5 号球"的日志条目
      const potEntry = logEntries.find(entry =>
        entry.includes('进了 5 号球') && !entry.includes('犯规')
      );

      console.log('黄金5进球日志:', potEntry);

      if (potEntry) {
        const triggerId = this.extractPlayerIdFromLog(potEntry, players, '进了');
        console.log('黄金5触发者ID:', triggerId);
        if (triggerId !== null) {
          const result = {
            type: 'golden5' as const,
            triggerId,
            ballId: 5,
            description: '开球轮合法进5号球'
          };
          console.log('返回黄金5事件信息:', result);
          return result;
        }
      }
    }

    // 检测普通黑5事件
    const normalBlack5Entry = logEntries.find(entry =>
      entry.includes('特殊事件: 普通黑5')
    );

    if (normalBlack5Entry) {
      const triggerId = this.extractPlayerIdFromLog(normalBlack5Entry, players, '扣除');
      if (triggerId !== null) {
        return {
          type: 'normalBlack5',
          triggerId,
          ballId: 5,
          description: '非开球轮犯规进5号球'
        };
      }
    }

    return {
      type: null,
      triggerId: null
    };
  }

  /**
   * 从日志中提取选手ID
   * @param logEntry 日志条目
   * @param players 选手信息
   * @param keyword 关键词（如'扣除'）
   * @returns 选手ID或null
   */
  private static extractPlayerIdFromLog(
    logEntry: string,
    players: Array<{id: number; name: string}>,
    keyword?: string
  ): number | null {
    // 构建正则表达式
    const pattern = keyword
      ? new RegExp(`选手 ([^\\s]+) ${keyword}`)
      : new RegExp(`选手 ([^\\s!，。；]+)`);

    const match = logEntry.match(pattern);
    if (match && match[1]) {
      const playerName = match[1];
      const player = players.find(p => p.name === playerName);
      return player ? player.id : null;
    }

    return null;
  }

  /**
   * 检查选手是否是特殊事件的触发者
   * @param playerId 选手ID
   * @param specialEvent 特殊事件信息
   * @returns 是否是触发者
   */
  static isEventTrigger(playerId: number, specialEvent: SpecialEventInfo): boolean {
    return specialEvent.triggerId === playerId;
  }

  /**
   * 获取选手的进球记录（考虑特殊事件）
   * @param playerId 选手ID
   * @param playerBalls 从游戏状态获取的进球记录
   * @param specialEvent 特殊事件信息
   * @returns 进球记录数组
   */
  static getPlayerBalls(
    playerId: number,
    playerBalls: Array<{id: number; points: number; isFoul: boolean}>,
    specialEvent: SpecialEventInfo
  ): Array<{id: number; points: number; isFoul: boolean}> {
    // 如果没有特殊事件，返回去重后的原始进球记录
    if (!specialEvent.type) {
      return this.deduplicateBalls(playerBalls);
    }

    // 如果是特殊事件的触发者
    if (this.isEventTrigger(playerId, specialEvent)) {
      // 根据特殊事件类型处理
      switch (specialEvent.type) {
        case 'blackGolden5':
        case 'normalBlack5':
          // 黑金5/普通黑5事件：只显示5号球（犯规）
          return [{
            id: 5,
            points: 0,
            isFoul: true
          }];
        case 'golden5':
          // 黄金5事件：只显示5号球（非犯规）
          return [{
            id: 5,
            points: 0,
            isFoul: false
          }];
        default:
          return this.deduplicateBalls(playerBalls);
      }
    } else {
      // 如果不是触发者，返回去重后的原始进球记录（排除特殊事件相关的球）
      const filteredBalls = playerBalls.filter(ball => {
        // 如果是5号球相关的特殊事件，其他选手不显示5号球
        if ((specialEvent.type === 'blackGolden5' ||
             specialEvent.type === 'golden5' ||
             specialEvent.type === 'normalBlack5') && ball.id === 5) {
          return false;
        }
        return true;
      });
      return this.deduplicateBalls(filteredBalls);
    }
  }

  /**
   * 去重进球记录
   * @param balls 进球记录数组
   * @returns 去重后的进球记录数组
   */
  private static deduplicateBalls(
    balls: Array<{id: number; points: number; isFoul: boolean}>
  ): Array<{id: number; points: number; isFoul: boolean}> {
    const seen = new Set<number>();
    const result: Array<{id: number; points: number; isFoul: boolean}> = [];

    for (const ball of balls) {
      if (!seen.has(ball.id)) {
        seen.add(ball.id);
        result.push(ball);
      }
    }

    return result.sort((a, b) => a.id - b.id);
  }

  /**
   * 获取特殊事件的显示信息
   * @param specialEvent 特殊事件信息
   * @returns 显示配置
   */
  static getEventDisplayConfig(specialEvent: SpecialEventInfo): {
    show: boolean;
    text: string;
    color: 'error' | 'success' | 'warning';
  } {
    if (!specialEvent.type) {
      return { show: false, text: '', color: 'error' };
    }

    switch (specialEvent.type) {
      case 'blackGolden5':
        return {
          show: true,
          text: '特殊事件: 黑金5!',
          color: 'error'
        };
      case 'golden5':
        return {
          show: true,
          text: '特殊事件: 黄金5!',
          color: 'success'
        };
      case 'normalBlack5':
        return {
          show: true,
          text: '特殊事件: 普通黑5!',
          color: 'warning'
        };
      default:
        return { show: false, text: '', color: 'error' };
    }
  }
}
