# 台球计分系统 - 待办事项

## 优先级高

1. **让球逻辑实现**
   - [ ] 在 `RuleEngine.ts` 中更新 `handleLetShot` 方法
   - [ ] 在 `ScoringPage.tsx` 中添加让球按钮和处理逻辑
   - [ ] 实现让球后的选手顺序调整

2. **得分计算优化**
   - [ ] 在 `ScoringPage.tsx` 中实现 `updateScores` 辅助函数
   - [ ] 优化 `logScoreChanges` 函数，使其更清晰地显示分数变化
   - [ ] 修复进球分数计算中的占位符问题

3. **小金逻辑完善**
   - [ ] 在 `RuleEngine.ts` 中实现非开球轮清台（小金）的逻辑
   - [ ] 添加小金分数计算
   - [ ] 在 UI 中添加小金事件的显示

4. **比赛结束判断**
   - [ ] 完善 `checkMatchEnd` 方法
   - [ ] 实现基于局数、分数的比赛结束判断
   - [ ] 添加比赛结束对话框和统计信息

## 优先级中

5. **UI 改进**
   - [ ] 优化球选择对话框
   - [ ] 改进比赛记录显示
   - [ ] 优化选手状态显示

6. **犯规处理完善**
   - [ ] 完善犯规分数的分配逻辑
   - [ ] 实现犯规池功能
   - [ ] 添加连续犯规的处理

7. **配置模板功能**
   - [ ] 实现配置模板的保存功能
   - [ ] 实现配置模板的加载功能
   - [ ] 添加预设模板选项

## 优先级低

8. **比赛时间限制功能**
   - [ ] 实现计时器组件
   - [ ] 添加比赛剩余时间显示
   - [ ] 实现基于时间的比赛结束判断

9. **移动端适配**
   - [ ] 优化移动设备上的布局
   - [ ] 改进触摸操作体验
   - [ ] 确保在小屏幕上的可用性

10. **测试扩展**
    - [ ] 为 `RuleEngine` 添加更多测试用例
    - [ ] 实现组件测试
    - [ ] 添加集成测试

## 已知问题

1. **让球逻辑缺失**
   - 当前让球功能被禁用，需要根据新的 `RuleConfig` 结构重新实现

2. **得分计算中的占位符**
   - 在 `ScoringPage.tsx` 中，进球的分数使用了占位符 (0)，需要使用实际分数

3. **小金逻辑未完成**
   - 非开球轮清台（小金）的逻辑尚未实现

4. **比赛结束判断不完整**
   - 当前比赛结束判断逻辑简化，需要完善

5. **犯规池功能未完全实现**
   - 犯规池的累积和使用逻辑需要完善

6. **配置模板功能缺失**
   - 配置模板的保存和加载功能尚未实现
