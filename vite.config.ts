import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 8080, // 尝试使用更高的端口（通常大于 1024 的端口不需要管理员权限）
    strictPort: false, // 如果 8080 被占用，允许 Vite 使用下一个可用端口
    host: true, // 监听所有网络接口，而不仅仅是 localhost
  },
  resolve: {
    alias: {
      // 配置路径别名，使其与 tsconfig.json 中的 paths 配置一致
      // 这允许使用绝对路径导入，例如 import Component from 'components/Component'
      '@': path.resolve(__dirname, './src'),
      'components': path.resolve(__dirname, './src/components'),
      'pages': path.resolve(__dirname, './src/pages'),
      'types': path.resolve(__dirname, './src/types'),
      'utils': path.resolve(__dirname, './src/utils'),
      'assets': path.resolve(__dirname, './src/assets'),
    }
  }
})
